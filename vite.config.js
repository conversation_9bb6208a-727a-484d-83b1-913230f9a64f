import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  build: {
    outDir: 'front'
  },
  server: {
    host: '0.0.0.0',
    proxy: {
      '/api': {
        // target: 'http://**************:1323',
        // target: 'http://**************:8918',
        // target: 'https://app.finanx.ai',
        // target: 'http://journey.moling.tech:81',
        target: 'https://mirror.moling.tech',
        // target: 'https://front.finanx.token.moling.tech',
        // target: 'http://sys.fnxai.token.moling.tech',
        changeOrigin: true
        // rewrite: (path) => path.replace(/^\/api\/core/, '')
      }
    }
  }
})
