# finanx-ai

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Customize configuration

See [Vite Configuration Reference](https://vitejs.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Compile and Minify for Production
打包环境配置目录```src/config/index.js```中的env参数
```sh
npm run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```


## 链接钱包工具
#### 1、前往 https://cloud.walletconnect.com/ 创建项目
#### 2、点击```Setup and Verify Domains```前往设置并验证https域名，下载walletconnect.txt文件并在服务器配置```/.well-known/walletconnect.txt```
- <span style="color:red;">注意: 域名需要https，否则会报错
 {context: 'core'} {context: 'core/relayer'} Error: WebSocket connection closed abnormally with code: 3000 (Unauthorized: origin not allowed)
</span>
#### 3、 根据配置修改```src/utils/web3-setuo.js```文件中```projectId```和```metadata```参数












