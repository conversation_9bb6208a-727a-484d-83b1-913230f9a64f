{"name": "fnxai-front", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@nguyenshort/vue3-mitt": "^0.0.4", "@reown/appkit": "^1.5.0", "@reown/appkit-adapter-wagmi": "^1.5.0", "@tanstack/vue-query": "^5.60.6", "@wagmi/connectors": "^5.4.0", "@wagmi/core": "^2.14.6", "@wagmi/vue": "^0.0.64", "axios": "^1.7.7", "bignumber.js": "^9.1.2", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "element-plus": "^2.8.1", "lodash": "^4.17.21", "pinia": "^2.1.7", "pinia-plugin-persist": "^1.0.0", "sass": "^1.79.1", "viem": "^2.21.48", "vue": "^3.4.29", "vue-i18n": "9", "vue-router": "^4.3.3"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "prettier": "^3.2.5", "vite": "^5.3.1"}}