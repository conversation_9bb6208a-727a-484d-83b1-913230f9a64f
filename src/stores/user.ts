import { getUserInfo } from '@/api/user'
import { defineStore } from 'pinia'

export const useUserStore = defineStore('userCounter', {
  state: () => {
    let user
    return { user }
  },
  actions: {
    clearUser() {
      this.user = undefined
    },
    async getUserMsg() {
      const res = await getUserInfo()
      this.user = res
      return res
    },
    editNickname(nickname) {
      if (this.user) {
        this.user.nickname = nickname
      }
    }
  },
  persist: {
    enabled: true
  }
})
