import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/home/<USER>'
import StakeView from '../views/stake/index.vue'
import UserView from '../views/user/index.vue'
import MyStake from '../views/user/children/MyStake/index.vue'
import AvailableBlanace from '../views/user/children/AvailableBlanace/index.vue'
import AvailableBlanaceMore from '../views/user/children/AvailableBlanaceMore/index.vue'
import MyList from '../views/user/children/MyList/index.vue'
import MyListNext from '../views/user/children/MyListNext/index.vue'
import DailyBonus from '../views/user/children/DailyBonus/index.vue'
import BonusWallet from '../views/user/children/BonusWallet/index.vue'
import AccountIncomeRecord from '../views/user/children/AccountIncomeRecord/index.vue'
import TransferView from '../views/transfer/index.vue'
import SharesWallet from '../views/user/children/SharesWallet/index.vue'
import DividendsList from '../views/user/children/DividendsList/index.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView
    },
    {
      path: '/stake',
      name: 'stake',
      component: StakeView
    },
    {
      path: '/transfer',
      name: 'transfer',
      component: TransferView
    },
    {
      path: '/user',
      name: 'user',
      component: UserView,
      redirect: '/user/myStake',
      children: [
        {
          path: '/',
          name: 'myStake'
        },
        {
          path: 'myStake',
          name: 'myStake',
          component: MyStake,
        },
        {
          path: 'availableBlanace',
          name: 'availableBlanace',
          component: AvailableBlanace,
        },
        {
          path: 'availableBlanaceMore',
          name: 'availableBlanaceMore',
          component: AvailableBlanaceMore,
        },
        {
          path: 'myList',
          name: 'myList',
          component: MyList,
        },
        {
          path: 'myListNext',
          name: 'myListNext',
          component: MyListNext,
        },
        {
          path: 'dailyBonus',
          name: 'dailyBonus',
          component: DailyBonus,
        },
        {
          path: 'bonusWallet',
          name: 'bonusWallet',
          component: BonusWallet,
        },
        {
          path: 'accountIncomeRecord',
          name: 'accountIncomeRecord',
          component: AccountIncomeRecord,
        },
        {
          path: 'sharesWallet',
          name: 'sharesWallet',
          component: SharesWallet,
        },
        {
          path: 'dividendsList',
          name: 'dividendsList',
          component: DividendsList,
        },
      ]
    }
  ]
})

export default router
