:root {
  --primary-color: #fff;
  --color-background: #020e24;
  --color-text: #fff;
  --color-tertiary-text: rgba(255, 255, 255, 0.5);
  --color-error: #ff3737;
  --modal-bg: #2e343f;
  --primary-bg: #13e6bc;
}
:root {
  --header-width: 1200px;
  --header-height: 90px;
  --header-background: transparent;
  --content-width: 1102px;
  --footer-height: 95px;
  --footer-background: #161f2e;
}

@media (min-width: 1536px) {
  :root {
    --header-height: 100px;
    --footer-height: 105px;
    --content-width: 1260px;
  }
}
@media (max-width: 1024px) {
  :root {
    --content-width: 100%;
    --header-height: 80px;
    --footer-height: 118px;
    --header-background: var(--color-background);
  }
}
@media (max-width: 767px) {
  :root {
    --content-width: 100%;
    --header-height: 66px;
    --footer-height: 108px;
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

ul,
li {
  list-style: none;
  margin-block-start: 0;
}

input::placeholder {
  color: var(--color-tertiary-text);
}
input::-moz-placeholder {
  color: var(--color-tertiary-text);
}
input::-ms-input-placeholder {
  color: var(--color-tertiary-text);
}
input::-webkit-input-placeholder {
  color: var(--color-tertiary-text);
}

body {
  color: var(--color-text);
  background: var(--color-background);
  line-height: 1.4;
  font-family: 'PingFangSC', 'PingFang SC', 'MicrosoftYaHei', '微软雅黑';
  font-size: 1em;
  min-height: 100vh;

  --w3m-z-index: 9999;
}
input {
  font-family: 'PingFangSC', 'PingFang SC', 'MicrosoftYaHei', '微软雅黑';
  caret-color: var(--primary-color);
  outline: none;
  border: none;
  background: transparent;
  color: var(--primary-color);
}
/* WebKit,Edge,Blink、 */
input::-webkit-input-placeholder {
  color: rgba(255, 255, 255, 0.3);
}

/* Mozilla Firefox 4 to 18 */
input:-moz-placeholder {
  color: rgba(255, 255, 255, 0.3);
}

/* Mozilla Firefox 19+ */
input::-moz-placeholder {
  color: rgba(255, 255, 255, 0.3);
}

/* Internet Explorer 10-11 */
input:-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.3);
}

/* Microsoft Edge */
input::-ms-input-placeholder {
  color: rgba(255, 255, 255, 0.3);
}
a {
  color: var(--color-text);
  text-decoration: none;
}

.router-link-exact-active {
  color: #13e6bc;
}
.noData {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.5);
  padding: 20px 0;
  text-align: center;
}
.cursor_p {
  cursor: pointer;
}
.c_13E6BC {
  color: #13e6bc;
}

.t_cursor_p {
  cursor: pointer;
  text-decoration: underline;
}

.no_record {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0 40px;
  img {
    height: 79px;
  }
  .txt {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
    line-height: 17px;
  }
}
@media screen and (min-width: 1024px) {
  .no_record {
    padding: 60px 0 50px;
    img {
      height: 120px;
    }
    .txt {
      font-size: 14px;
      line-height: 20px;
    }
  }
}

.c_error {
  color: var(--color-error);
}
