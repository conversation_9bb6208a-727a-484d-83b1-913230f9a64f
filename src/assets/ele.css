body {
  --el-mask-color: rgba(255, 255, 255, 0.2);
  --el-color-primary: #fff;
}

.el-tabs__item {
  font-size: 13px !important;
  color: #fff !important;
  padding: 9px 12px 14px !important;
  font-weight: 400 !important;
  margin-right: 5px !important;
}
.el-tabs__item:last-child {
  margin-right: 0 !important;
}
.el-tabs__item.is-active {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-radius: 21px !important;
}
.el-tabs__nav-wrap:after {
  content: initial !important;
  width: 0 !important;
}
.el-tabs__nav-prev,
.el-tabs__nav-next {
  display: none !important;
}
.el-tabs__nav-wrap.is-scrollable {
  padding: 0 !important;
}
.el-tabs__active-bar {
  height: 4px !important;
  background-color: #13e6bc !important;
  border-radius: 2px !important;
}
.magif_table.el-table {
  --el-table-border-color: #5e5f60;
  --el-table-header-bg-color: #434956;
  --el-table-header-text-color: rgba(255, 255, 255, 0.5);
  --el-table-text-color: #fff;
  --el-table-bg-color: #2a303b;
  --el-table-expanded-cell-bg-color: #2f3642;
  --el-table-border: 1px solid #676775;
  --el-table-tr-bg-color: #2a303b;
  --el-fill-color-lighter: #2f3642;
  --table-row-hover-bg-color: #434956;
  --el-table-current-row-bg-color: #434956;
  border-radius: 5px;
  border: 1px solid #5e5f60;
  font-size: 12px;
  .el-table__cell {
    padding: 6px 0;
  }
  .cell {
    text-overflow: unset;
    word-break: break-word;
    overflow: initial;
    text-align: center;
    padding: 0 10px;
  }

  .icon-copy {
    width: 17px;
    margin: 0 5px;
  }
  .nickname {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.el-table--border .el-table__inner-wrapper:after,
.el-table__inner-wrapper:before,
.el-table__border-left-patch,
.el-table--border:after,
.el-table--border:before {
  background-color: transparent !important;
  width: 0 !important;
}
.el-table .el-table__row:last-child .el-table__cell {
  border-bottom: none;
}
.el-table .el-table__row:hover .el-table__cell {
  background-color: #434956 !important;
}
.el-table td.el-table__cell div {
  font-size: 12px;
}

.table_pagination {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-right: 15px;
  margin-top: 21px;
  .table_pagination_total {
    font-size: 12px;
    color: #abafb6;
  }
  .el-pagination {
    margin-top: 8px;
    --el-pagination-font-size: 14px;
    --el-pagination-bg-color: transparent;
    --el-pagination-text-color: rgba(255, 255, 255, 0.66);
    --el-pagination-border-radius: 6px;
    --el-pagination-button-color: rgba(255, 255, 255, 0.66);
    --el-pagination-button-width: 32px;
    --el-pagination-button-height: 32px;
    --el-pagination-button-disabled-color: rgba(255, 255, 255, 0.6);
    --el-pagination-button-disabled-bg-color: transparent;
    --el-pagination-button-bg-color: rgba(255, 255, 255, 0);
    --el-pagination-hover-color: rgba(255, 255, 255, 0.19);
  }
  .more.el-icon svg {
    width: 20px;
    height: 20px;
  }
  .el-pager li {
    margin: 0 4px;
  }
  .el-pager li.is-active,
  .el-pager li:hover {
    background: rgba(255, 255, 255, 0.19);
    border: 1px solid #13e6bc;
    color: #13e6bc;
  }
}

.drawer-wrapper.el-drawer {
  border-radius: 15px 15px 0px 0px;
  --el-drawer-bg-color: #2e343f;
  --el-drawer-padding-primary: 0;
  height: auto !important;
  max-height: 500px;
  min-height: 300px;
  .el-drawer__header {
    margin-bottom: 0;
  }
}

.flowRecord-list {
  width: 100%;
  margin-top: 18px;
  .flowRecord-item {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 9px 0;
    cursor: pointer;
    .icon {
      width: 24px;
      flex-shrink: 0;
      margin-right: 9px;
    }
    .icon-transfer {
      width: 26px;
      margin-left: -1px;
      margin-right: 8px;
    }
    .content {
      width: 100%;
      overflow: hidden;
      .txt1 {
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        font-size: 15px;
        color: #ffffff;
        line-height: 19px;
      }
      .txt2 {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        line-height: 15px;
        margin-top: 3px;
      }
    }
    .value-content {
      flex-shrink: 0;
      margin-left: 9px;
    }
    .value {
      font-size: 15px;
      color: #ffffff;
      line-height: 21px;
      text-align: right;
    }
    .value-extra {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.7);
      line-height: 20px;
      text-align: right;
    }
  }
  .flowRecord-item-month {
    display: flex;
    border-bottom: 1px solid #474747;
    align-items: center;
    padding-bottom: 11px;
    margin: 9px 0;
    .icon {
      width: 5px;
      height: 5px;
      background: #13e6bc;
      border: 1px solid #b2feef;
      border-radius: 50%;
      flex-shrink: 0;
      margin-right: 6px;
    }
    .title {
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
      flex-shrink: 0;
    }
    .balance {
      flex: 1;
      font-size: 13px;
      color: rgba(255, 255, 255, 0.7);
      line-height: 17px;
      text-align: right;
    }
  }
}

.form-input.el-input {
  --el-input-text-color: rgba(255, 255, 255, 0.5);
  --el-input-border: none;
  --el-input-hover-border: none;
  --el-input-focus-border: none;
  --el-input-transparent-border: none;
  --el-input-border-color: transparent;
  --el-input-border-radius: 0;
  --el-input-bg-color: transparent;
  --el-input-icon-color: rgba(255, 255, 255, 0.5);
  --el-input-placeholder-color: rgba(255, 255, 255, 0.5);
  --el-input-hover-border-color: transparent;
  --el-input-clear-hover-color: transparent;
  --el-input-focus-border-color: transparent;
  --el-input-width: 100%;
  --el-input-height: 35px;
  box-sizing: border-box;
  display: inline-flex;
  font-size: 25px;
  line-height: 35px;
  position: relative;
  vertical-align: middle;
  width: 100%;
  .el-input__wrapper {
    padding: 0;
  }
}

.search-select-wraper {
  .search-select {
    .el-select__wrapper {
      padding: 0;
      background-color: transparent;
      border-radius: 0;
      box-shadow: none;
    }
    .el-select__wrapper.is-hovering:not(.is-focused) {
      box-shadow: none;
    }
    .el-select__suffix {
      display: none;
    }
    .el-select__placeholder {
      color: #fff;
    }
  }
}

.el-popper > .el-popper__arrow:before {
  display: none;
}
.el-popper {
  background: #353c48 !important;
  box-shadow: 0px 0 4px 0px rgba(0, 0, 0, 0.5) !important;
  border: none !important;
}

.el-select-dropdown__item.search-select-option {
  color: #fff;
}
.el-select-dropdown__item.search-select-option.is-selected {
  color: #14d1ad;
  background: none;
}
.el-select-dropdown__item.search-select-option.is-hovering {
  background-color: #434956;
  color: #14d1ad;
}

.el-popper {
  max-width: 120px;
  .el-select-dropdown {
    max-width: 120px;
  }
  .el-select-dropdown,
  .el-dropdown-menu {
    --el-font-size-base: 13px;
  }
}

.el-popper.is-customized {
  background: #999;
  color: #fff;
}

.el-popper.is-customized .el-popper__arrow::before {
  background: #999;
}
.el-popper.is-customized.home-popper {
  font-size: 14px;
  border-radius: 10px;
  padding: 10px 11px;
  max-width: 220px;
  line-height: 1.214285;
}
@media (max-width: 1024px) {
  .el-popper.is-customized.home-popper {
    font-size: 11px;
    max-width: 260px;
  }
}

.flowRecord-list {
  margin-top: 12px;
  .flowRecord-item {
    padding: 15px 0;
    border-bottom: 1px solid #292930;
    .content {
      .txt1 {
        font-size: 16px;
        line-height: 20px;
      }
      .txt2 {
        font-size: 14px;
        line-height: 17px;
        margin-top: 1px;
      }
    }
    .value {
      font-size: 16px;
      line-height: 22px;
    }
  }
  .flowRecord-item-month {
    padding-bottom: 21px;
    margin: 25px 0 5px;
    .title {
      font-size: 16px;
      line-height: 22px;
    }
    .balance {
      font-size: 16px;
      line-height: 20px;
    }
  }
}
.el-loading-mask {
  --el-mask-color: rgba(255, 255, 255, 0.05);
}

/* 隐藏原生箭头 */
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

@media screen and (min-width: 1536px) {
}

@media screen and (min-width: 1024px) {
  .el-tabs__item {
    height: 52px !important;
    font-size: 16px !important;
    padding: 12px 16px 18px !important;
    margin-right: 20px !important;
  }
  .el-tabs__item.is-active {
    border-radius: 26px !important;
    border: none !important;
    box-shadow: none !important;
  }
  .el-table td.el-table__cell div {
    font-size: 14px !important;
  }
  .magif_table.el-table {
    font-size: 14px;
    .el-table__cell {
      padding: 20px 0;
    }
    .cell {
      padding: 0 7px;
    }
  }
  .table_pagination {
    flex-direction: row;
    justify-content: end;
    padding-right: 0;
    .table_pagination_total {
      font-size: 14px;
      margin-right: 7px;
    }
    .el-pagination {
      margin-top: 0;
    }
  }
  .drawer-wrapper.el-drawer {
    border-radius: 15px;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    right: inherit !important;
    transform: translate(-50%, -50%); /* 使 Drawer 居中 */
    width: 30% !important; /* 根据需要调整宽度 */
    height: auto !important; /* 自定义高度 */
    max-height: 520px;
    transition: all 0.3s ease; /* 添加动画效果 */
  }

  .el-popper {
    max-width: 189px;
    .el-select-dropdown,
    .el-dropdown-menu {
      --el-font-size-base: 14px;
    }
  }
}
