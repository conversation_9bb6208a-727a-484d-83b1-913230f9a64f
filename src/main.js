import './assets/main.css'

import { createApp } from 'vue'
import VueMitter from '@nguyenshort/vue3-mitt'
import { createPinia } from 'pinia'
import piniaPersist from 'pinia-plugin-persist'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import en from 'element-plus/es/locale/lang/en'

import App from './App.vue'
import router from './router'
import i18n, { getDefaultLocale } from './i18n'
import { WagmiPlugin } from '@wagmi/vue'
import { QueryClient, VueQueryPlugin } from '@tanstack/vue-query'
import { wagmiAdapter } from '@/config'

const queryClient = new QueryClient()

const app = createApp(App)

const pinia = createPinia()
pinia.use(piniaPersist)
app.use(pinia)
app.use(router)
app.use(i18n)
app.use(VueMitter)
app.use(WagmiPlugin, { config: wagmiAdapter.wagmiConfig })
app.use(VueQueryPlugin, { queryClient })

const locale = getDefaultLocale()
app.use(ElementPlus, {
  locale: locale == 'zh-CN' ? zhCn : en
})
app.mount('#app')
