<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { formatDate, formatPrice, isLogin, isMobileWeb } from '@/utils/index.js'
import { getPageProductOrderIncome, getProductOrderSumAmount } from '@/api/user.js'
import { CHAINS, CHAINS_CONFIG } from '@/config'
import { getAccount, watchAccount } from '@wagmi/core'
import DrawerSendFrom from '@/views/user/components/DrawerSendFrom/index.vue'
import DrawerFlowDetails from '@/views/user/components/DrawerFlowDetails/index.vue'
import StakeModal from '@/views/home/<USER>/StakeModal.vue'
import { useUserStore } from '@/stores/user'
import { getProductAccountInfo } from '@/api/stake.js'
import { useI18n } from 'vue-i18n'

const router = useRouter()
const userStore = useUserStore()

const { t } = useI18n()

// {0:收益,1:提币,3:定期质押收益 4: 复投 5：划转}
const typeList = {
  0: t('dailyInterestAmount'),
  1: t('WithdrawDict'),
  3: t('stakingReward'),
  4: t('Restake'),
  5: t('transfer'),
}

const totalAmount = ref('')
const list = ref([])
const drawerSendFromVisible = ref(false)
const drawerFlowDetailsVisible = ref(false)
const selectData = ref()
const noData = ref(false)

const chains = CHAINS_CONFIG.chains

const goBack = () => {
  router.back()
}
const goMore = () => {
  router.push({
    name: 'availableBlanaceMore',
    query: {
      active: 'myStake'
    }
  })
}

const onChangeDrawerSendFromVisible = (v) => {
  drawerSendFromVisible.value = v
}

const showSendFrom = () => {
  onChangeDrawerSendFromVisible(true)
}

const onChangeDrawerFlowDetailsVisible = (v) => {
  drawerFlowDetailsVisible.value = v
}

const showFlowDetails = (item) => {
  selectData.value = item
  onChangeDrawerFlowDetailsVisible(true)
}
const showStakeFlag = computed(() => {
  return userStore.user.accountIncomeStatus == '2'
})
const stakeVisible = ref(false)
const showStakeModal = () => {
  if (isMobileWeb()) {
    router.push({
      name: 'stake',
      query: {
        type: 'reinvest',
        accountType: '0'
      }
    })
  } else {
    stakeVisible.value = true
  }
}
const stakeSuccess = async () => {
  stakeVisible.value = false
  let { chainId } = getAccount(CHAINS_CONFIG)
  const chain = chains.find((chain) => chain.id === chainId)
  if (chainId && !chain) {
    chainId = CHAINS[0].id
  }
  try {
    const res = await getProductAccountInfo({ chainId })
    totalAmount.value = res.availableBalance
  } catch (e) {
    //
  }
}

const getInit = async () => {
  if (isLogin()) {
    let { chainId } = getAccount(CHAINS_CONFIG)
    const chain = chains.find((chain) => chain.id === chainId)
    if (chainId && !chain) {
      chainId = CHAINS[0].id
    }
    try {
      noData.value = false
      const res = await getProductAccountInfo({ chainId })
      const resList = await getPageProductOrderIncome({ chainId })
      noData.value = resList.list.length <= 0
      totalAmount.value = res.availableBalance
      list.value = resList.list
    } catch (e) {
      //
    }
  }
}

const onSendSuccess = () => {
  onChangeDrawerSendFromVisible(false)
  // getInit();
}

onMounted(async () => {
  await getInit()
})

const unwatchAccount = watchAccount(CHAINS_CONFIG, {
  async onChange(network, prevNetwork) {
    if (network.chainId != prevNetwork.chainId) {
      await getInit()
    }
  }
})

onUnmounted(() => {
  unwatchAccount()
})
</script>

<template>
  <div class="availableBlanace-container">
    <div class="availableBlanace-top-wrap">
      <img
        @click="goBack"
        class="icon-left cursor_p"
        src="@/assets/images/user/icon_left_circular.png"
      />
      <div class="availableBlanace-top">
        <div class="txt1">{{ $t('Available Balance') }}:</div>
        <div class="txt2">{{ formatPrice(totalAmount) }} FNXAI</div>
        <div class="btn-wrap">
          <div class="btn-send btn-reinvest cursor_p" @click="showStakeModal" v-if="showStakeFlag">
            {{ $t('Reinvest') }}
          </div>
          <div class="btn-send cursor_p" @click="showSendFrom">{{ $t('Send') }}</div>
        </div>
      </div>
    </div>
    <div class="transactions-title">
      <div class="title">{{ $t('Transactions') }}</div>
      <div class="more cursor_p" @click="goMore">
        <div class="txt">{{ $t('More') }}</div>
        <img class="icon-right" src="@/assets/images/arrow_right.png" />
      </div>
    </div>
    <div class="flowRecord-list">
      <div
        class="flowRecord-item cursor_p"
        v-for="item in list"
        :key="item.id"
        @click="showFlowDetails(item)"
      >
        <!--   type {0:收益,1:提币,3:定期质押收益 4: 复投 5：划转}     -->
        <img
          v-if="item.type === '0' || item.type === '3'"
          class="icon"
          src="@/assets/images/user/icon-in.png"
        />
        <img v-if="item.type === '1' || item.type === '4'" class="icon" src="@/assets/images/user/icon-out.png" />
        <img
          v-if="item.type === '5'"
          class="icon icon-transfer"
          src="@/assets/images/user/round_transfer.png"
        />
        <div class="content">
          <div class="txt1">{{ typeList[item.type] }}</div>
          <div class="txt2">{{ formatDate(item.updateDatetime, 'yyyy年MM月dd日') }}</div>
        </div>
        <div class="value-content">
          <div class="value">{{ formatPrice(item.transAmount) }} FNXAI</div>
        </div>
      </div>
      <div class="no_record" v-if="noData">
        <img src="@/assets/images/no_record.png" />
        <div class="txt">{{ $t('No records') }}</div>
      </div>
    </div>
    <stake-modal
      type="reinvest"
      accountType="0"
      :visible="stakeVisible"
      :availableBalance="totalAmount"
      @cancel="stakeVisible = false"
      @success="stakeSuccess"
    />
    <DrawerSendFrom
      :visible="drawerSendFromVisible"
      :type="'0'"
      @onChangeVisible="onChangeDrawerSendFromVisible"
      @onSuccess="onSendSuccess"
    />

    <DrawerFlowDetails
      :visible="drawerFlowDetailsVisible"
      :id="selectData?.id"
      @onChangeVisible="onChangeDrawerFlowDetailsVisible"
    />
  </div>
</template>

<style scoped>
.availableBlanace-container {
  padding-right: 15px;
  padding-top: 4px;
  .availableBlanace-top-wrap {
    width: 100%;
    height: 125px;
    position: relative;
    .icon-left {
      width: 35px;
      position: absolute;
      top: 7px;
      left: 7px;
      z-index: 10;
    }
    .availableBlanace-top {
      width: 100%;
      height: 125px;
      background-image: url('@/assets/images/user/blanace_bg.png');
      background-size: 100% 100%;
      position: relative;
      z-index: 9;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .txt1 {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        line-height: 17px;
      }
      .txt2 {
        margin-top: 8px;
        font-size: 17px;
        color: #ffffff;
        line-height: 24px;
      }
      .btn-wrap {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
      }
      .btn-send {
        margin-top: 11px;
        min-width: 57px;
        padding: 0 10px;
        background: #13e6bc;
        border-radius: 6px;
        font-size: 13px;
        color: #ffffff;
        line-height: 23px;
        text-align: center;
      }
    }
  }
  .transactions-title {
    display: flex;
    margin-top: 21px;
    justify-content: space-between;
    .title {
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
    }
    .more {
      display: flex;
      .txt {
        font-size: 12px;
        color: #ffffff;
        line-height: 17px;
      }
      .icon-right {
        width: 17px;
      }
    }
  }
}
@media screen and (min-width: 1024px) {
  .availableBlanace-container {
    padding-right: 0;
    .availableBlanace-top-wrap {
      height: 95px;
      position: relative;
      .icon-left {
        width: 36px;
        top: 0;
        left: 0;
      }
      .availableBlanace-top {
        width: calc(100% - 48px);
        height: 95px;
        background-image: url('@/assets/images/user/web_blanace_bg.png');
        margin-left: 48px;
        align-items: start;
        padding: 0 25px;
        .txt1 {
          font-size: 14px;
          line-height: 20px;
        }
        .txt2 {
          font-size: 20px;
          line-height: 28px;
        }
        .btn-send {
          position: absolute;
          bottom: 20px;
          right: 18px;
          margin-top: 0;
          width: 82px;
          border-radius: 6px;
          font-size: 15px;
          color: #ffffff;
          line-height: 30px;
          &.btn-reinvest {
            right: 110px;
          }
        }
      }
    }
    .transactions-title {
      margin-top: 23px;
      .title {
        font-size: 16px;
        line-height: 22px;
      }
      .more {
        display: flex;
        .txt {
          font-size: 14px;
          line-height: 20px;
        }
        .icon-right {
          width: 18px;
        }
      }
    }
  }
}
</style>
