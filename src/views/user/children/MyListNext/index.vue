<script setup>
import { computed, ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getPageTeamMembers, getTeamMembersSum, getUserInfo } from '@/api/user'
import CustomTable from '@/components/CustomTable/index.vue'
import { formatDate, truncateString, isPhone, formatPrice } from '@/utils/index.js'
import ClipboardJS from 'clipboard'
import message from '@/utils/message'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

const childRef = ref()
const userId = ref()
const nickname = ref('')
const totalAmount = ref()
const amountUsdt = ref()
const checkFlag = ref()

const columns = computed(() => {
  let row = [
    {
      titleKey: '空白',
      dataIndex: 'pic',
      width: 50
    },
    {
      titleKey: 'Referral Date',
      dataIndex: 'refereeDatetime',
      render: (v) => formatDate(v, 'dd/MM/yyyy hh:mm:ss'),
      width: isPhone() ? 100 : 130
    },
    {
      titleKey: 'Address',
      dataIndex: 'address',
      width: isPhone() ? 95 : null
    },
    {
      titleKey: 'Name',
      dataIndex: 'nickname',
      width: isPhone() ? 80 : null
    },
    {
      titleKey: 'Amount Staked(FNXAI)',
      dataIndex: 'amount',
      width: isPhone() ? 110 : 130,
      render: (v) => formatPrice(v)
    },
    {
      titleKey: 'Amount Staked(USDT)',
      dataIndex: 'amountUsdt',
      width: isPhone() ? 110 : 130,
      render: (v) => formatPrice(v)
    }
  ]
  if (checkFlag.value === '1') {
    row = row.concat([
      {
        titleKey: 'Community Staked(FNXAI)',
        dataIndex: 'teamAmount',
        width: isPhone() ? 140 : 130,
        render: (v) => formatPrice(v)
      },
      {
        titleKey: 'Community Staked(USDT)',
        dataIndex: 'teamAmountUsdt',
        width: isPhone() ? 140 : 130,
        render: (v) => formatPrice(v)
      }
    ])
  }
  row = row.concat([
    {
      titleKey: 'Network',
      width: isPhone() ? 120 : 140,
      dataIndex: 'cuserTeamMembersChainResList'
    }
  ])

  return row
})

const showPointer = (row) => {
  return row.refereeFlag == '1'
}

const goBack = () => {
  router.back()
}
const loadData = async (query) => {
  try {
    const res = await getTeamMembersSum(query.userId)
    const userInfo = await getUserInfo()

    checkFlag.value = userInfo.checkFlag
    userId.value = query.userId
    nickname.value = query.nickname
    amountUsdt.value = res.amountUsdt
    totalAmount.value = res.amount
    if (childRef.value) {
      childRef.value.hanldeReset()
    }
  } catch (e) {
    console.error(e)
  }
}

watch(
  () => route.query,
  (newQuery) => {
    loadData(newQuery)
  }
)

onMounted(async () => {
  try {
    const query = route.query
    await loadData(query)
  } catch (e) {
    //
  }
})

const onHandleCopy = (event, value) => {
  if (!value || value == '') {
    return
  }
  // 创建 ClipboardJS 实例并绑定到点击的元素
  const clipboard = new ClipboardJS(event.target, {
    text: () => value // 要复制的内容
  })

  // 监听复制成功事件
  clipboard.on('success', () => {
    message.success(t('copySuc'))
    // 复制成功后销毁 Clipboard 实例
    clipboard.destroy()
  })

  // 监听复制失败事件
  clipboard.on('error', () => {
    // alert('复制失败');
    // 复制失败后也销毁 Clipboard 实例
    clipboard.destroy()
  })

  // 手动触发点击事件，执行复制操作
  clipboard.onClick(event)
}

const handleCellClick = (row) => {
  // refereeFlag是否有下级且当前登录用户是否查看图谱权限 0:无(无权限) 1:有  用户是否有查看图谱权限checkFlag
  if (checkFlag.value === '1' && row.refereeFlag == '1') {
    router.push({
      path: `/user/myListNext`,
      query: {
        userId: row.userId,
        nickname: row.nickname,
        active: 'myList'
      }
    })
  }
}
</script>

<template>
  <div class="myListNext-container">
    <div class="myList-top">
      <div class="name">
        <img
          class="icon-left cursor_p"
          src="@/assets/images/user/icon_left_circular.png"
          @click="goBack"
        />
        <div class="name-txt">{{ $t('Name') }}: {{ nickname }}</div>
      </div>
      <div class="txt-wrap">
        <div class="txt">
          {{ $t('Total Staked') }}: <samp>{{ formatPrice(totalAmount ?? 0) }} FNXAI</samp>
        </div>
        <div class="txt">
          {{ $t('Total Staked') }}: <samp>{{ formatPrice(amountUsdt ?? 0) }} usdt</samp>
        </div>
      </div>
    </div>
    <div class="table-contaner">
      <CustomTable
        ref="childRef"
        :columns="columns"
        :search-params="{ userId }"
        :get-page-list="getPageTeamMembers"
        :stripe="true"
        :noInit="true"
        :showPointer="showPointer"
        @cellClick="handleCellClick"
      >
        <template #pic="{ value, data }">
          <div class="pic-wrap">
            <img :src="value" />
            <span
              v-if="
                data.extraIncomeRate > 0 &&
                data.nodeId != '8' &&
                data.nodeId != '9' &&
                data.nodeId != '10' &&
                data.nodeId != '11'
              "
              :class="{
                initial: data.levelZeroFlag == '1'
              }"
              >{{ data.extraIncomeRate }}</span
            >
          </div>
        </template>
        <template #nickname="{ value, data }">
          <el-tooltip class="box-item" effect="dark" :content="`${value}`" placement="top-end">
            <div
              class="nickname"
              :style="{ cursor: data.refereeFlag == '1' ? 'pointer' : 'default' }"
            >
              {{ value }}
            </div>
          </el-tooltip>
        </template>
        <template #address="{ value }">
          <div class="address-wrap">
            {{ truncateString(value) }}
            <img
              class="icon-copy cursor_p"
              src="@/assets/images/copy_white.png"
              @click.stop="(e) => onHandleCopy(e, value)"
            />
          </div>
        </template>
        <template #cuserTeamMembersChainResList="{ value }">
          <el-space wrap @click.stop>
            <template v-for="item in value" :key="item.chainName">
              <el-tooltip
                class="box-item"
                effect="dark"
                :content="`${$t('Amount Staked(USDT)')}:\n${formatPrice(item.amountUsdt)}`"
                placement="top-end"
              >
                <div style="cursor: default">{{ item.chainName }}</div>
              </el-tooltip>
            </template>
          </el-space>
        </template>
      </CustomTable>
    </div>
  </div>
</template>

<style scoped>
.myListNext-container {
  .myList-top {
    margin-bottom: 13px;
    margin-top: 5px;
    display: flex;
    flex-direction: column;
    .name {
      display: flex;
      align-items: center;
      margin-bottom: 7px;
      .icon-left {
        height: 35px;
        margin-right: 7px;
      }
      .name-txt {
        font-size: 18px;
        color: #ffffff;
      }
    }
    .txt-wrap {
      display: flex;
      flex-direction: column;
      .txt:first-child {
        margin-bottom: 5px;
      }
    }
    .txt {
      font-size: 15px;
      line-height: 21px;
      color: #ffffff;
      samp {
        color: #13e6bc;
      }
    }
  }

  @media (min-width: 1024px) {
    .myList-top {
      margin-bottom: 18px;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .name {
        margin-bottom: 0;
      }
      .txt-wrap {
        flex-direction: row;
        .txt:first-child {
          margin-right: 50px;
          margin-bottom: 0;
        }
      }
      .txt {
        font-size: 16px;
      }
    }
  }
}
</style>
<style lang="scss">
.myListNext-container {
  .pic-wrap {
    --size: 22px;
    --size-text: 12.5px;
    position: relative;
    width: var(--size);
    height: var(--size);
    margin: 0 auto;
    span {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: var(--size-text) !important;
      font-weight: 600;
      transform: scale(0.7);
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      line-height: 1;
      &.initial {
        color: #666;
      }
    }
    img {
      object-fit: cover;
      width: var(--size);
      height: var(--size);
    }
  }
}
@media (min-width: 1024px) {
  .myListNext-container {
    .pic-wrap {
      --size: 24px;
      --size-text: 15px;
    }
  }
}
</style>
