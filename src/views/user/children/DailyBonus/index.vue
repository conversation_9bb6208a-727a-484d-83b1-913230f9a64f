<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { getPageProductOrderReward } from '@/api/user'
import CustomTable from '@/components/CustomTable/index.vue';
import { CHAINS_CONFIG } from '@/config'
import {
  watchAccount,
} from '@wagmi/core'
import { formatDate, formatPrice, isLogin, isPhone, truncateString } from '@/utils'
import DrawerNextDisburesmentDate from '@/views/user/components/DrawerNextDisburesmentDate/index.vue'
import message from '@/utils/message.js'
import ClipboardJS from 'clipboard';
import { useI18n } from 'vue-i18n';
import { getProductOrderSum } from '@/api/stake.js'

const { t } = useI18n();


const childRef = ref();
const productOrderSum = ref();
const drawerNextDisburesmentDateVisible = ref(false)
const selectRow = ref()

const columns = computed(() => [
  {
    titleKey: 'S/N',
    dataIndex: 'refId',
    width: 95,
    render: (v) => v.slice(0, 9) + ' ' + v.slice(9)
  },
  {
    titleKey: 'Staked Time',
    dataIndex: 'buyTime',
    render: (v) => formatDate(v, 'dd/MM/yyyy hh:mm:ss'),
    width: 104,
  },
  {
    titleKey: 'Address',
    dataIndex: 'address',
    width: 100,
  },
  {
    titleKey: 'Name',
    dataIndex: 'nickname',
  },
  {
    titleKey: 'Staked Amount (FNXAI)',
    dataIndex: 'amount',
    width: isPhone() ? 110 : 120,
    render: (v) => formatPrice(v),
  },
  {
    titleKey: 'dailyInterestRate',
    dataIndex: 'dailyInterestAmount',
    width: isPhone() ? 110 : 100,
    render: (v) => formatPrice(v),
  },
  {
    titleKey: 'Completed Days',
    dataIndex: 'incomeDay',
    width: 90,
  },
  {
    titleKey: 'Next Disbursement Date',
    dataIndex: 'nextIncomeTime',
    width: isPhone() ? 140 : 110,
  },
  {
    titleKey: 'Balance Bonus',
    dataIndex: 'amountCanDelivery',
    width: isPhone() ? 110 : 120,
    render: (v) => formatPrice(v),
  },
  {
    titleKey: 'Network',
    dataIndex: 'chainName',
  },
]);


const onChangeDrawerNextDisburesmentDateVisible = (v) => {
  drawerNextDisburesmentDateVisible.value = v ?? false
}

const handleNextDisburesmentClick = (row) => {
  onChangeDrawerNextDisburesmentDateVisible(true)
  selectRow.value = row
}


const onHandleCopy = (event, value) => {
  if(!value || value == "") {
    return
  }
// 创建 ClipboardJS 实例并绑定到点击的元素
  const clipboard = new ClipboardJS(event.target, {
    text: () => value,  // 要复制的内容
  });

  // 监听复制成功事件
  clipboard.on('success', () => {
    message.success(t('copySuc'))
    // 复制成功后销毁 Clipboard 实例
    clipboard.destroy();
  });

  // 监听复制失败事件
  clipboard.on('error', () => {
    // alert('复制失败');
    // 复制失败后也销毁 Clipboard 实例
    clipboard.destroy();
  });

  // 手动触发点击事件，执行复制操作
  clipboard.onClick(event);
}

const getInit = async () => {
  if (isLogin()) {
    childRef.value.hanldeReset();

    try {
      const sum = await getProductOrderSum({ type: '1'});
      productOrderSum.value = sum;
    } catch (e) {
      //
    }
  }
}

onMounted(async () => {
  await getInit();
})

const unwatchAccount = watchAccount(CHAINS_CONFIG, {
  async onChange(network, prevNetwork) {
    if (network.chainId != prevNetwork.chainId) {
      await getInit()
    }
  }
})

onUnmounted(() => {
  unwatchAccount()
})

</script>

<template>
  <div class="dailyBonus-container">
    <div class="myStake-top-wrap">
      <div class="myStake-top">
        <div class="myStake-top-r">
          <div class="availableBlanace-item">
            <div class="txt1">{{ $t('Received') }}:</div>
            <div class="txt2">{{ formatPrice(productOrderSum?.received ?? 0) }} FNXAI</div>
          </div>
          <div class="availableBlanace-item">
            <div class="txt1">{{ $t('Unclaimed') }}:</div>
            <div class="txt2">{{ formatPrice(productOrderSum?.balance ?? 0) }} FNXAI</div>
          </div>
        </div>
      </div>
    </div>
    <div class="table-contaner">
      <CustomTable ref="childRef"
                   :columns="columns"
                   :get-page-list="getPageProductOrderReward"
                   :stripe="true"
                   :noInit="true"
      >
        <template #address="{ value }">
          <div class="address-wrap">
            {{truncateString(value)}}
          </div>
          <img
            class="icon-copy cursor_p"
            src="@/assets/images/copy_white.png"
            @click.stop="(e) => onHandleCopy(e, value)"
          />
        </template>
        <template #nickname="{ value }">
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="`${value}`"
            placement="top-end"
          >
            <div class="nickname" style="cursor: default">{{ value }}</div>
          </el-tooltip>
        </template>
        <template #incomeDay="{ data, value }">
          <!-- 状态{0:质押中,1:已到期} -->
          <div class="c_13E6BC" v-if="data.status == '1'">{{ $t('Completed') }}</div>
          <div v-else>{{ value }}/{{ data.lockTime }}</div>
        </template>
        <template #nextIncomeTime="{ data, value }">
          <!-- 状态{0:质押中,1:已到期} -->
          <div class="c_13E6BC" v-if="data.status == '1'">{{ $t('Completed') }}</div>
          <div class="t_cursor_p" v-else  @click="handleNextDisburesmentClick(data)">{{ formatDate(value, 'dd/MM/yyyy') }}</div>
        </template>
      </CustomTable>
    </div>
    <DrawerNextDisburesmentDate
      :visible="drawerNextDisburesmentDateVisible"
      :productOrderId="selectRow?.refId"
      :type="'1'"
      @onChangeVisible="onChangeDrawerNextDisburesmentDateVisible"
    />
  </div>
</template>

<style scoped>
.dailyBonus-container{
  .myStake-top-wrap {
    width: 100%;
    position: relative;
    margin-bottom: 21px;
    padding-right: 15px;
    .myStake-top {
      width: 100%;
      height: 65px;
      background-image: url('@/assets/images/user/dailyBonus_bg.png');
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      padding: 0 19px;

      .myStake-top-r {
        width: 100%;
        display: flex;
        justify-content: space-between;
        .availableBlanace-item {
          width: 100%;
          position: relative;
          &:last-child {
            text-align: right;
            &:after {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 1px;
              height: 28px;
              background-color: rgba(255, 255, 255, 0.2);
            }
          }
        }
        .txt1 {
          font-size: 10px;
          color: rgba(255, 255, 255, 0.7);
          line-height: 14px;
        }
        .txt2 {
          font-size: 12px;
          color: #ffffff;
          line-height: 16px;
          margin-top: 3px;
        }
      }

    }

  }
}

@media (min-width: 1024px) {
  .dailyBonus-container {
    .myStake-top-wrap {
      position: relative;
      padding-right: 0;
      .myStake-top {
        height: 95px;
        background-image: url('@/assets/images/user/web_dailyBonus_bg.png');
        .myStake-top-r {
          justify-content: end;
          margin-top: 5px;
          .availableBlanace-item {
            text-align: center;
            &:last-child {
              text-align: center;
              &:after {
                height: 33px;
              }
            }
          }
          .txt1 {
            font-size: 13px;
            line-height: 18px;
          }
          .txt2 {
            font-size: 14px;
            line-height: 20px;
          }
        }
      }
    }

  }
}
</style>
