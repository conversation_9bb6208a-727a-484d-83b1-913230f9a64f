<script setup>
import { ref, onMounted } from 'vue'
import DrawerSharesWalletJourDetails from '@/views/user/components/DrawerSharesWalletJourDetails/index.vue'
import { getPageAccountIncome, getPageStockJour, getStockAccount } from '@/api/user.js'
import { formatDate, formatPrice, isLogin } from '@/utils/index.js'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const accountInfo = ref()
const list = ref([])
const loading = ref(true)
const noData = ref(false)
const isEnd = ref(false)
const pageNum = ref(1);

const userInfo = ref()

const getList = async () => {
  if (isLogin()) {
    try {
      loading.value = true
      noData.value = false
      const resList = await getPageStockJour({
        pageNum: pageNum.value,
        pageSize: 100,
      })
      loading.value = false

      if (resList.pages <= pageNum.value) {
        isEnd.value = true
      }
      if(pageNum.value == 1) {
        list.value = resList.list
        noData.value = resList.list.length <= 0
      } else {
        const l = list.value.slice();
        list.value = [...l, ...resList.list];
      }
    } catch (e) {
      loading.value = false
    }
  }
}

const onLoadMore  = async () => {
  pageNum.value ++;
  getList()
}

const getInit = async () => {
  if (isLogin()) {
    try {
      noData.value = false
      const res = await getStockAccount()
      const info = await userStore.getUserMsg()
      userInfo.value = info
      accountInfo.value = res
      await getList();
    } catch (e) {
      //
    }
  }
}
const drawerFlowDetailsVisible = ref(false)
const selectData = ref()
const onChangeDrawerFlowDetailsVisible = (v) => {
  drawerFlowDetailsVisible.value = v
}
const showFlowDetails = (item) => {
  selectData.value = item
  onChangeDrawerFlowDetailsVisible(true)
}

onMounted(async () => {
  getInit()
})
</script>

<template>
  <div class="availableBlanace-container">
    <div class="availableBlanace-top-wrap">
      <div class="availableBlanace-top">
        <div class="availableBlanace-top-l">
          <div class="txt1">{{ $t('Total Asset') }}:</div>
          <div class="txt2">
            <div class="txt">{{ formatPrice(accountInfo?.totalBalance ?? 0) }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="transactions-title">
      <div class="title">{{ $t('Transactions') }}</div>
    </div>
    <div class="flowRecord-list">
      <div
        class="flowRecord-item cursor_p"
        v-for="item in list"
        :key="item.id"
        @click="showFlowDetails(item)"
      >
        <!--   type  {0:每日奖励,1:邀请奖励, 2:提币, 3股东分红, 5:卓越奖励, 6:复投, 8:转账}     -->
        <img
          v-if="item.transAmount < 0"
          class="icon"
          src="@/assets/images/user/icon-out.png"
        />
        <img v-else class="icon" src="@/assets/images/user/icon-in.png" />
        <div class="content">
          <div class="txt1">{{ item.bizNote }}</div>
          <div class="txt2">{{ formatDate(item.createTime, 'yyyy年MM月dd日') }}</div>
        </div>
        <div class="value-content">
          <div class="value">{{ formatPrice(item.transAmount) }}</div>
        </div>
      </div>
      <div class="no_record" v-if="noData">
        <img src="@/assets/images/no_record.png" />
        <div class="txt">{{ $t('No records') }}</div>
      </div>
      <div class="more-w" v-if="!loading && !noData && !isEnd">
        <div class="more" @click="onLoadMore">{{ $t('Load more') }}</div>
      </div>
    </div>
    <DrawerSharesWalletJourDetails
      :visible="drawerFlowDetailsVisible"
      :id="selectData?.id"
      @onChangeVisible="onChangeDrawerFlowDetailsVisible"
    />
  </div>
</template>

<style scoped>
.availableBlanace-container {
  padding-right: 15px;
  padding-top: 4px;
  .availableBlanace-top-wrap {
    width: 100%;
    position: relative;
    .availableBlanace-top {
      width: 100%;
      background-image: url('@/assets/images/user/blanace_bg.png');
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      padding: 0 19px;

      .availableBlanace-top-l {
        width: 100%;
        position: relative;
        margin-top: 17px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding-bottom: 9px;
        .txt1 {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          line-height: 17px;
        }
        .txt2 {
          font-size: 17px;
          color: #ffffff;
          line-height: 24px;
          display: flex;
          align-items: start;
          justify-content: space-between;
          .txt {
            margin-top: 4px;
          }
        }
      }
    }
  }
  .transactions-title {
    display: flex;
    margin-top: 21px;
    justify-content: space-between;
    .title {
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
    }
    .more {
      display: flex;
      .txt {
        font-size: 12px;
        color: #ffffff;
        line-height: 17px;
      }
      .icon-right {
        width: 17px;
      }
    }
  }
  .more-w{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px 0 15px;
    .more{
      width: fit-content;
      border-radius: 6px;
      font-size: 15px;
      color: #13e6bc;
      text-align: center;
      flex-shrink: 0;
      padding: 3px 15px;
      cursor: pointer;
    }
  }
}
@media screen and (min-width: 1024px) {
  .availableBlanace-container {
    padding-right: 0;
    .availableBlanace-top-wrap {
      height: 95px;
      position: relative;
      .availableBlanace-top {
        height: 95px;
        background-image: url('@/assets/images/user/web_blanace_bg.png');
        align-items: center;
        padding: 0 25px;
        flex-direction: row;
        .availableBlanace-top-l {
          border-bottom: none;
          margin-top: 0;
          padding-bottom: 0;
          .txt1 {
            font-size: 14px;
            line-height: 20px;
          }
          .txt2 {
            font-size: 20px;
            line-height: 28px;
            justify-content: start;
            align-items: center;
            margin-top: 7px;
            .txt {
              margin-top: 0;
            }
          }
        }
      }
    }
    .transactions-title {
      margin-top: 23px;
      .title {
        font-size: 16px;
        line-height: 22px;
      }
      .more {
        display: flex;
        .txt {
          font-size: 14px;
          line-height: 20px;
        }
        .icon-right {
          width: 18px;
        }
      }
    }
    .more-w{
      padding: 40px 0 20px;
      .more{
        border-radius: 6px;
        font-size: 15px;
        padding: 3px 15px;
      }
    }
  }
}
</style>
