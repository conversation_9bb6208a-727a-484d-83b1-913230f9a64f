<script setup>
import { computed, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPageTeamMembers, getTeamMembersSum, getUserInfo } from '@/api/user'
import CustomTable from '@/components/CustomTable/index.vue'
import { formatDate, formatPrice, isLogin, isPhone, truncateString } from '@/utils'
import ClipboardJS from 'clipboard'
import message from '@/utils/message'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
// 权限
const router = useRouter()

const childRef = ref()
const userInfo = ref()
const totalAmount = ref()
const amountUsdt = ref()

const columns = computed(() => {
  let row = [
    {
      titleKey: '空白',
      dataIndex: 'pic',
      width: 50
    },
    {
      titleKey: 'Referral Date',
      dataIndex: 'refereeDatetime',
      render: (v) => formatDate(v, 'dd/MM/yyyy hh:mm:ss'),
      width: isPhone() ? 100 : 130
    },
    {
      titleKey: 'Address',
      dataIndex: 'address',
      width: isPhone() ? 95 : null
    },
    {
      titleKey: 'Name',
      dataIndex: 'nickname',
      width: isPhone() ? 80 : null
    },
    {
      titleKey: 'Amount Staked(FNXAI)',
      dataIndex: 'amount',
      width: isPhone() ? 110 : 130,
      render: (v) => formatPrice(v)
    },
    {
      titleKey: 'Amount Staked(USDT)',
      dataIndex: 'amountUsdt',
      width: isPhone() ? 110 : 130,
      render: (v) => formatPrice(v)
    }
  ]
  if (userInfo.value.checkFlag === '1') {
    row = row.concat([
      {
        titleKey: 'Community Staked(FNXAI)',
        dataIndex: 'teamAmount',
        width: isPhone() ? 140 : 130,
        render: (v) => formatPrice(v)
      },
      {
        titleKey: 'Community Staked(USDT)',
        dataIndex: 'teamAmountUsdt',
        width: isPhone() ? 140 : 130,
        render: (v) => formatPrice(v)
      }
    ])
  }
  row = row.concat([
    {
      titleKey: 'Network',
      width: 140,
      dataIndex: 'cuserTeamMembersChainResList'
    }
  ])

  return row
})

const handleCellClick = (row) => {
  // refereeFlag是否有下级且当前登录用户是否查看图谱权限 0:无(无权限) 1:有  用户是否有查看图谱权限checkFlag
  if (row.refereeFlag == '1') {
    router.push({
      path: `/user/myListNext`,
      query: {
        userId: row.userId,
        nickname: row.nickname,
        active: 'myList'
      }
    })
  }
}

onMounted(async () => {
  if (isLogin()) {
    try {
      const info = await getUserInfo()
      const res = await getTeamMembersSum()
      userInfo.value = info
      amountUsdt.value = res.amountUsdt
      totalAmount.value = res.amount
    } catch (e) {
      //
    }
  }
})

const showPointer = (row) => {
  return row.refereeFlag == '1'
}

const onHandleCopy = (event, value) => {
  if (!value || value == '') {
    return
  }
  // 创建 ClipboardJS 实例并绑定到点击的元素
  const clipboard = new ClipboardJS(event.target, {
    text: () => value // 要复制的内容
  })

  // 监听复制成功事件
  clipboard.on('success', () => {
    message.success(t('copySuc'))
    // 复制成功后销毁 Clipboard 实例
    clipboard.destroy()
  })

  // 监听复制失败事件
  clipboard.on('error', () => {
    // alert('复制失败');
    // 复制失败后也销毁 Clipboard 实例
    clipboard.destroy()
  })

  // 手动触发点击事件，执行复制操作
  clipboard.onClick(event)
}
</script>

<template>
  <div class="myList-container">
    <div class="myList-top">
      <div class="txt">
        {{ $t('Total Staked') }}: <samp>{{ formatPrice(totalAmount ?? 0) }} FNXAI</samp>
      </div>
      <div class="txt">
        {{ $t('Total Staked') }}: <samp>{{ formatPrice(amountUsdt ?? 0) }} usdt</samp>
      </div>
    </div>
    <div class="table-contaner myList-table-contaner" v-if="userInfo">
      <CustomTable
        ref="childRef"
        :columns="columns"
        :get-page-list="getPageTeamMembers"
        :stripe="true"
        :noInit="!isLogin()"
        @cellClick="handleCellClick"
        :showPointer="showPointer"
        columnsClassName="myList-table-columns"
      >
        <template #pic="{ value, data }">
          <div class="pic-wrap">
            <img :src="value" />
            <span
              v-if="
                data.extraIncomeRate > 0 &&
                data.nodeId != '8' &&
                data.nodeId != '9' &&
                data.nodeId != '10' &&
                data.nodeId != '11'
              "
              :class="{
                initial: data.levelZeroFlag == '1'
              }"
              >{{ data.extraIncomeRate }}</span
            >
          </div>
        </template>
        <template #address="{ value }">
          <div class="address-wrap">
            {{ truncateString(value) }}
            <img
              class="icon-copy cursor_p"
              src="@/assets/images/copy_white.png"
              @click.stop="(e) => onHandleCopy(e, value)"
            />
          </div>
        </template>
        <template #nickname="{ value, data }">
          <el-tooltip class="box-item" effect="dark" :content="`${value}`" placement="top-end">
            <div
              class="nickname"
              :style="{ cursor: data.refereeFlag == '1' ? 'pointer' : 'default' }"
            >
              {{ value }}
            </div>
          </el-tooltip>
        </template>

        <template #cuserTeamMembersChainResList="{ value }">
          <el-space wrap @click.stop>
            <template v-for="item in value" :key="item.chainName">
              <el-tooltip
                class="box-item"
                effect="dark"
                :content="`${$t('Amount Staked(USDT)')}:\n${formatPrice(item.amountUsdt)}`"
                placement="top-end"
              >
                <div style="cursor: default">{{ item.chainName }}</div>
              </el-tooltip>
            </template>
          </el-space>
        </template>
      </CustomTable>
    </div>
  </div>
</template>

<style scoped>
.myList-container {
  .myList-top {
    width: 100%;
    margin-bottom: 13px;
    margin-top: 5px;
    display: flex;
    flex-direction: column;
    .txt {
      font-size: 15px;
      color: #ffffff;
      line-height: 21px;
      samp {
        color: #13e6bc;
      }
    }
    .txt:first-child {
      margin-bottom: 5px;
    }
  }
  @media (min-width: 1024px) {
    .myList-top {
      margin-bottom: 18px;
      justify-content: space-between;
      flex-direction: row;
      align-items: center;
      .txt {
        font-size: 16px;
      }
      .txt:first-child {
        margin-bottom: 0px;
      }
    }
  }
}
</style>
<style lang="scss">
.myList-container {
  .pic-wrap {
    --size: 22px;
    --size-text: 12.5px;
    position: relative;
    width: var(--size);
    height: var(--size);
    margin: 0 auto;
    span {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: var(--size-text) !important;
      font-weight: 600;
      transform: scale(0.7);
      text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      line-height: 1;
      &.initial {
        color: #666;
      }
    }
    img {
      object-fit: cover;
      width: var(--size);
      height: var(--size);
    }
  }
}
@media (min-width: 1024px) {
  .myList-container {
    .pic-wrap {
      --size: 24px;
      --size-text: 15px;
    }
  }
}
</style>
