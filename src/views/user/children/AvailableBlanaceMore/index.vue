<script setup>
import { ref, onMounted, onUnmounted} from 'vue'
import { useRouter } from 'vue-router';
import { formatDate, formatPrice, isLogin } from '@/utils/index.js'
import { getPageProductOrderIcomeByMonth, getProductOrderSumAmount } from '@/api/user.js'
import { CHAINS, CHAINS_CONFIG } from '@/config'
import {
  getAccount,
  watchAccount
} from '@wagmi/core'
import DrawerFlowDetails from '@/views/user/components/DrawerFlowDetails/index.vue'
import { useI18n } from 'vue-i18n'

const router = useRouter();
const { t } = useI18n()

// {0:收益,1:提币,3:定期质押收益 4: 复投 5：划转}
const typeList = {
  0: t('dailyInterestAmount'),
  1: t('WithdrawDict'),
  3: t('stakingReward'),
  4: t('Restake'),
  5: t('transfer'),
}
const chains = CHAINS_CONFIG.chains

const sumAmount = ref();
const list = ref([]);

const drawerFlowDetailsVisible = ref(false);
const selectData = ref();
const loading = ref(false);
const noData = ref(false);

const onChangeDrawerFlowDetailsVisible = (v) => {
  drawerFlowDetailsVisible.value = v;
}

const showFlowDetails = (item) => {
  selectData.value = item;
  onChangeDrawerFlowDetailsVisible(true);
}

const goBack = () => {
  router.back();
}

const getInit = async () => {
  if (isLogin()) {
    let { chainId } = getAccount(CHAINS_CONFIG)
    const chain = chains.find(chain => chain.id === chainId)
    if (chainId && !chain) {
      chainId = CHAINS[0].id
    }
    try {
      loading.value = true;
      noData.value = false;
      const res = await getProductOrderSumAmount({chainId});
      const resList = await getPageProductOrderIcomeByMonth({chainId});
      noData.value = resList.list.length <= 0;
      loading.value = false;
      list.value = resList.list;
      sumAmount.value = res;
    } catch (e) {
      loading.value = false;
    }
  }
}
onMounted(async () => {
  await getInit()
})

const unwatchAccount = watchAccount(CHAINS_CONFIG, {
  async onChange(network, prevNetwork) {
    if (network.chainId != prevNetwork.chainId) {
      await getInit()
    }
  }
})

onUnmounted(() => {
  unwatchAccount()
})
</script>

<template>
  <div class="availableBlanace-container"  v-loading="loading">
    <div class="availableBlanace-top-wrap">
      <img @click="goBack" class="icon-left cursor_p" src="@/assets/images/user/icon_left_circular.png">
      <div class="availableBlanaceMore-top">
        <div class="item">
          <img class="icon" src="@/assets/images/user/icon-in.png">
          <div class="txt-w">
            <div class="txt1">{{$t('Received')}}</div>
            <div class="txt2">{{sumAmount?.inAmount}} FNXAI</div>
          </div>
        </div>
        <div class="item">
          <img class="icon" src="@/assets/images/user/icon-out.png">
          <div class="txt-w">
            <div class="txt1">{{$t('Withdraw')}}</div>
            <div class="txt2">{{sumAmount?.outAmount}} FNXAI</div>
          </div>
        </div>
      </div>
    </div>
    <div class="flowRecord-list">
      <template v-for="(data, index) in list" :key="data.month.month">
        <div class="flowRecord-item-month">
          <div class="icon"></div>
          <div class="title">{{data.month.month}}</div>
          <div class="balance">{{index == 0 ? $t('Current balance') : $t('Closing balance')}}: {{formatPrice(data.month.monthSumAmount)}} FNXAI</div>
        </div>
        <div class="flowRecord-item" v-for="item in data.productOrderIncomeList" :key="item.id" @click="showFlowDetails(item)">
          <!--   type {0:收益,1:提币,3:定期质押收益 5：划转}     -->
          <img v-if="item.type === '0' || item.type === '3'" class="icon" src="@/assets/images/user/icon-in.png">
          <img v-if="item.type === '1' || item.type === '4'" class="icon" src="@/assets/images/user/icon-out.png">
          <img
            v-if="item.type === '5'"
            class="icon icon-transfer"
            src="@/assets/images/user/round_transfer.png"
          />
          <div class="content">
            <div class="txt1">{{ typeList[item.type] }}</div>
            <div class="txt2">{{ formatDate(item.updateDatetime, 'yyyy年MM月dd日')}}</div>
          </div>
          <div class="value-content">
            <div class="value">{{ formatPrice(item.transAmount) }} FNXAI</div>
          </div>
        </div>
      </template>

      <div class="no_record" v-if="noData">
        <img src="@/assets/images/no_record.png"/>
        <div class="txt">{{ $t('No records') }}</div>
      </div>
    </div>

    <DrawerFlowDetails
      :visible="drawerFlowDetailsVisible"
      :id="selectData?.id"
      @onChangeVisible="onChangeDrawerFlowDetailsVisible"/>
  </div>
</template>

<style scoped>
.availableBlanace-container{
  padding-right: 15px;
  padding-top: 4px;
  .availableBlanace-top-wrap{
    width: 100%;
    height: 125px;
    position: relative;
    .icon-left{
      width: 35px;
      position: absolute;
      top: 7px;
      left: 7px;
      z-index: 10;
    }
    .availableBlanaceMore-top{
      width: 100%;
      height: 125px;
      background-image: url("@/assets/images/user/blanace_bg.png");
      background-size: 100% 100%;
      position: relative;
      z-index: 9;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 20px;
      .item{
        width: 50%;
        height: 125px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        .icon{
          height: 24px;
        }
        .txt-w{
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          margin-top: 9px;
        }
        .txt1{
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          line-height: 17px;
        }
        .txt2{
          font-size: 17px;
          color: #FFFFFF;
          line-height: 24px;
          margin-top: 4px;
        }
      }
    }

  }

}
@media screen and (min-width: 1024px) {
  .availableBlanace-container{
    padding-right: 0;
    .availableBlanace-top-wrap{
      height: 95px;
      position: relative;
      .icon-left{
        width: 36px;
        top: 0;
        left: 0;
      }
      .availableBlanaceMore-top{
        width: calc(100% - 48px);
        height: 95px;
        background-image: url("@/assets/images/user/web_blanace_bg.png");
        margin-left: 48px;
        align-items: start;
        .item{
          height: 95px;
          .icon{
            height: 30px;
          }
          .txt-w{
            flex-direction: row;
            margin-top: 8px;
          }
          .txt1{
            font-size: 16px;
            color: #FFFFFF;
            line-height: 22px;
          }
          .txt2{
            font-size: 16px;
            line-height: 22px;
            margin-top:0;
            margin-left: 5px;
          }
        }

      }
    }
  }
}
</style>
