<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { formatDate, formatPrice, isPhone } from '@/utils/index.js'
import { getPageVipDividendRecord, getVipDividendAccount } from '@/api/user.js'
import { CHAINS_CONFIG } from '@/config'
import { getAccount, watchAccount } from '@wagmi/core'
import CustomTable from '@/components/CustomTable/index.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()

const statusList = [
  {
    key: '0',
    value: t('Not reviewed')
  },
  {
    key: '1',
    value: t('Approved')
  },
  {
    key: '2',
    value: t('Issued')
  },
  {
    key: '3',
    value: t('Rejected after review')
  },
  {
    key: 'all',
    value: t('ALL')
  }
]

const chains = CHAINS_CONFIG.chains
const childRef = ref()
const status = ref('all')
const currentChainId = ref('')
const productOrderSum = ref()

const goBack = () => {
  router.back()
}

const columns = computed(() => [
  {
    titleKey: 'No.',
    dataIndex: 'index',
    width: 50,
  },
  {
    titleKey: 'Date',
    dataIndex: 'createDatetime',
    width: isPhone() ? 100 : 120
  },
  {
    titleKey: 'Serial Number',
    dataIndex: 'productOrderId',
    width: isPhone() ? 100 : null
  },
  {
    titleKey: 'Staked Time',
    dataIndex: 'buyTime',
    width: isPhone() ? 100 : 150
  },
  {
    titleKey: 'Staked Amount (FNXAI)',
    dataIndex: 'pledgeAmount',
    width: isPhone() ? 100 : 101,
    render: (v) => formatPrice(v)
  },
  {
    titleKey: 'Dividends Amount（FNXAI）',
    dataIndex: 'dividendPlatformCoin',
    width: isPhone() ? 100 : 102,
    render: (v) => formatPrice(v)
  },
  {
    titleKey: 'Percentage',
    dataIndex: 'rate',
    width: isPhone() ? 90 : 104
  },
  {
    titleKey: 'FNXAI : Shares Ratio',
    dataIndex: 'pledgeWeight',
    width: isPhone() ? 90 : 104
  },
  {
    titleKey: 'Entitled Shares',
    dataIndex: 'stockAmount',
    width: isPhone() ? 90 : 70
  },
  {
    titleKey: 'Distribution Date',
    dataIndex: 'duePaidDatetime',
    width: isPhone() ? 100 : 120
  },
])

const onSearch = () => {
  childRef.value.handleSearch()
}

const getInit = async () => {
  const { chainId } = getAccount(CHAINS_CONFIG)
  const chain = chains.find((chain) => chain.id === chainId)
  if (chainId && !chain) {
    return
  }
  currentChainId.value = chainId

  childRef.value.hanldeReset()
  try {
    const sum = await getVipDividendAccount({ chainId: chainId })
    productOrderSum.value = sum
  } catch (e) {
    //
  }
}

onMounted(async () => {
  await getInit()
})

const unwatchAccount = watchAccount(CHAINS_CONFIG, {
  async onChange(network, prevNetwork) {
    if (network.chainId != prevNetwork.chainId) {
      await getInit()
    }
  }
})

onUnmounted(() => {
  unwatchAccount()
})
</script>

<template>
  <div class="dividendsList-container">
    <div class="dividendsList-top-wrap">
      <img
        @click="goBack"
        class="icon-left cursor_p"
        src="@/assets/images/user/icon_left_circular.png"
      />
      <div class="dividendsList-top">
        <div class="dividendsList-top-l">
          <div class="txt1">{{ $t('Dividends') }}</div>
          <div class="txt2">
            <div class="txt">{{ formatPrice(productOrderSum?.dividends) }} FNXAI</div>
          </div>
        </div>
        <div class="dividendsList-top-r">
          <div class="dividendsList-item">
            <div class="txt1">{{ $t('Pending Distribution') }}</div>
            <div class="txt2">{{ formatPrice(productOrderSum?.pendingDistribution) }} FNXAI</div>
          </div>
          <div class="dividendsList-item">
            <div class="txt1">{{ $t('Distributed') }}</div>
            <div class="txt2">{{ formatPrice(productOrderSum?.distributed) }} FNXAI</div>
          </div>
        </div>
      </div>
    </div>
    <div class="dividendsList-search">
      <div class="dividendsList-search-label">{{ $t('Status') }}:</div>
      <div class="search-select-wraper">
        <el-select
          v-model="status"
          placeholder="Select"
          size="large"
          class="search-select"
          effect="dark"
          placement="bottom"
          :teleported="false"
        >
          <el-option
            v-for="item in statusList"
            :key="item.key"
            :label="item.value"
            :value="item.key"
            class="search-select-option"
          />
        </el-select>
        <img
          class="icon-search cursor_p"
          @click="onSearch"
          src="@/assets/images/user/icon-search.png"
        />
      </div>
    </div>
    <div class="table-contaner">
      <CustomTable
        ref="childRef"
        :columns="columns"
        :search-params="{ isPaid: status !== 'all' ? status : '', chainId: currentChainId }"
        :get-page-list="getPageVipDividendRecord"
        :stripe="true"
        :noInit="true"
      >
        <template #index="{ sIndex }">
          <div>{{ sIndex + 1 }}</div>
        </template>
        <template #createDatetime="{ value }">
          <div>{{ formatDate(value, 'dd/MM/yyyy') }}</div>
        </template>
        <template #buyTime="{ value }">
          <div>{{ formatDate(value, 'dd/MM/yyyy') }}</div>
          <div>{{ formatDate(value, 'hh:mm:ss') }}</div>
        </template>
        <template #rate="{ value }">
          <div>{{ value * 100 }}%</div>
        </template>
        <template #pledgeWeight="{ value }">
          <div>1: {{ value }}</div>
        </template>
        <template #duePaidDatetime="{ value }">
          <div>{{ formatDate(value, 'dd/MM/yyyy') }}</div>
        </template>
      </CustomTable>
    </div>
  </div>
</template>

<style>
.dividendsList-container {
  padding-right: 15px;
  padding-top: 4px;

  .dividendsList-top-wrap {
    width: 100%;
    position: relative;
    margin-bottom: 12px;
    padding-right: 15px;
    .icon-left {
      width: 35px;
      position: absolute;
      top: 7px;
      left: 7px;
      z-index: 10;
    }
    .dividendsList-top {
      width: 100%;
      height: 125px;
      background-image: url('@/assets/images/user/blanace_bg.png');
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      padding: 0 19px;

      .dividendsList-top-l {
        width: 100%;
        position: relative;
        margin-top: 17px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding-bottom: 9px;
        .txt1 {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          line-height: 17px;
          display: flex;
          align-items: center;
          justify-content: center;
          img{
            height: 16px;
            margin-left: 3px;
          }
        }
        .txt2 {
          font-size: 17px;
          color: #ffffff;
          line-height: 24px;
          display: flex;
          align-items: start;
          justify-content: center;
          .txt {
            margin-top: 4px;
          }
        }
      }
      .dividendsList-top-r {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-top: 9px;
        .dividendsList-item {
          width: 100%;
          position: relative;
          &:after {
            content: '';
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 1px;
            height: 28px;
            background-color: rgba(255, 255, 255, 0.2);
          }
          &:first-child {
            margin-right: 15px;
          }
          &:last-child {
            &:after{
              content: none !important;
            }
          }
        }
        .txt1 {
          font-size: 10px;
          color: rgba(255, 255, 255, 0.7);
          line-height: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          img{
            height: 16px;
            margin-left: 3px;
          }
        }
        .txt2 {
          font-size: 12px;
          color: #ffffff;
          line-height: 16px;
          margin-top: 4px;
          text-align: center;
        }
      }
    }
  }
  .dividendsList-search {
    display: flex;
    align-items: center;
    margin-top: 12px;
    margin-bottom: 12px;
    .dividendsList-search-label {
      font-size: 12px;
      color: #ffffff;
      display: none;
      margin-right: 7px;
    }
    .search-select-wraper {
      width: 100%;
      height: 40px;
      background: rgba(50, 56, 68, 0.94);
      border-radius: 6px;
      position: relative;
      .search-select {
        font-size: 12px;
        color: #ffffff;
        flex: 1;
        .el-popper {
          max-width: 100%;
        }
      }
      .el-select--large .el-select__wrapper {
        min-height: 40px;
        padding: 0 30px 0 12px;
      }
      .icon-search {
        width: 19px;
        flex-shrink: 0;
        margin-left: 5px;
        position: absolute;
        top: 50%;
        right: 7px;
        transform: translateY(-50%);
      }
    }
  }
}
@media screen and (min-width: 1024px) {
  .dividendsList-container {
    padding-right: 0;

    .dividendsList-top-wrap {
      position: relative;
      padding-right: 0;
      margin-bottom: 27px;
      height: 95px;
      .icon-left {
        width: 36px;
        top: 0;
        left: 0;
      }
      .dividendsList-top {
        width: calc(100% - 48px);
        height: 95px;
        background-image: url('@/assets/images/user/web_blanace_bg.png');
        margin-left: 48px;
        align-items: center;
        padding: 0 25px;
        flex-direction: row;
        .dividendsList-top-l {
          border-bottom: none;
          margin-top: 0;
          padding-bottom: 0;
          .txt1 {
            font-size: 14px;
            line-height: 20px;
            justify-content: start;
          }
          .txt2 {
            font-size: 20px;
            line-height: 28px;
            justify-content: start;
            align-items: center;
            margin-top: 7px;
            .txt {
              margin-top: 0;
            }
          }
        }
        .dividendsList-top-r {
          justify-content: end;
          margin-top: 5px;
          .dividendsList-item {
            width: 154px;
            text-align: center;
            &:first-child {
              margin-right: 0;
              &:after {
                height: 33px;
              }
            }
          }
          .txt1 {
            font-size: 13px;
            line-height: 18px;
          }
          .txt2 {
            font-size: 14px;
            line-height: 20px;
          }

        }
      }
    }
    .dividendsList-search {
      margin-top: 29px;
      .dividendsList-search-label {
        display: block;
        font-size: 16px;
        line-height: 37px;
      }
      .search-select-wraper {
        width: 235px;
        height: 37px;
        margin-left: 8px;
        border-radius: 7px;
        padding: 0 7px 0 12px;
        .search-select {
          font-size: 16px;
        }
        .icon-search {
          width: 22px;
        }
      }
    }
  }
}
</style>
