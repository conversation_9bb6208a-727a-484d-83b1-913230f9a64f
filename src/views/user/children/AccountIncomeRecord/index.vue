<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { formatDate, formatPrice, isLogin } from '@/utils/index.js'
import { getMyAccount, getPageAccountIncome, getProductOrderSumAmount } from '@/api/user.js'
import { CHAINS, CHAINS_CONFIG } from '@/config'
import { getAccount, watchAccount } from '@wagmi/core'
import DrawerAccountIncomeDetails from '@/views/user/components/DrawerAccountIncomeDetails/index.vue'
import { getDictListByParentKey } from '@/api/public.js'
import { useI18n } from 'vue-i18n'

const router = useRouter()
const { t } = useI18n()

// {0:每日奖励,1:邀请奖励, 2:提币, 3股东分红, 5:卓越奖励, 6:复投, 8:转账}
const typeList = {
  0: t('Daily Bonus'),
  1: t('Referral Bonus'),
  2: t('WithdrawDict'),
  3: t('Shareholder Bonus'),
  5: t('Extra Bonus'),
  6: t('Restake'),
  8: t('transfer')
}

const totalAmount = ref('')
const list = ref([])
const drawerFlowDetailsVisible = ref(false)
const selectData = ref()
const jourBizType = ref()
const jourBizTypeList = ref()
const accountInfo = ref()
const loading = ref(true)
const noData = ref(false)
const isEnd = ref(false)
const pageNum = ref(1);

const onChangeDrawerFlowDetailsVisible = (v) => {
  drawerFlowDetailsVisible.value = v
}

const showFlowDetails = (item) => {
  selectData.value = item
  onChangeDrawerFlowDetailsVisible(true)
}

const goBack = () => {
  router.back()
}

const getList = async () => {
  if (isLogin()) {
    try {
      loading.value = true
      noData.value = false
      const resList = await getPageAccountIncome({
        type: jourBizType.value != 'all' ? jourBizType.value : '',
        pageNum: pageNum.value,
        pageSize: 100,
      })
      loading.value = false

      if (resList.pages <= pageNum.value) {
        isEnd.value = true
      }
      if(pageNum.value == 1) {
        list.value = resList.list
        noData.value = resList.list.length <= 0
      } else {
        const l = list.value.slice();
        list.value = [...l, ...resList.list];
      }
    } catch (e) {
      loading.value = false
    }
  }
}

const onSearch = async () => {
  pageNum.value = 1;
  getList()
}

const onLoadMore  = async () => {
  pageNum.value ++;
  getList()
}

const chains = CHAINS_CONFIG.chains

const getInit = async () => {
  if (isLogin()) {
    let { chainId } = getAccount(CHAINS_CONFIG)
    const chain = chains.find((chain) => chain.id === chainId)
    if (chainId && !chain) {
      chainId = CHAINS[0].id
    }
    try {
      const res = await getProductOrderSumAmount({ chainId })
      const account = await getMyAccount()
      const dictList = await getDictListByParentKey('account_income_type')
      jourBizTypeList.value = [
        ...dictList,
        {
          id: 'all',
          key: 'all',
          value: t('ALL')
        }
      ]
      accountInfo.value = account
      totalAmount.value = res.totalAmount
    } catch (e) {
      //
    }
    getList()
  }
}

onMounted(async () => {
  await getInit()
})

const unwatchAccount = watchAccount(CHAINS_CONFIG, {
  async onChange(network, prevNetwork) {
    if (network.chainId != prevNetwork.chainId) {
      await getInit()
    }
  }
})

onUnmounted(() => {
  unwatchAccount()
})
</script>

<template>
  <div class="accountIncomeRecord-container" v-loading="loading">
    <div class="accountIncomeRecord-top-wrap">
      <img
        @click="goBack"
        class="icon-left cursor_p"
        src="@/assets/images/user/icon_left_circular.png"
      />
      <div class="accountIncomeRecord-top">
        <div class="item">
          <img class="icon" src="@/assets/images/user/icon-in.png" />
          <div class="txt-w">
            <div class="txt1">{{ $t('In') }}</div>
            <div class="txt2">{{ formatPrice(accountInfo?.inAmount) }} FNXAI</div>
          </div>
        </div>
        <div class="item">
          <img class="icon" src="@/assets/images/user/icon-out.png" />
          <div class="txt-w">
            <div class="txt1">{{ $t('Out') }}</div>
            <div class="txt2">{{ formatPrice(accountInfo?.outAmount) }} FNXAI</div>
          </div>
        </div>
      </div>
    </div>
    <div class="search-select-wraper">
      <el-select
        v-model="jourBizType"
        :placeholder="$t('Search')"
        size="large"
        class="search-select"
        effect="dark"
        :teleported="false"
      >
        <el-option
          v-for="item in jourBizTypeList"
          :key="item.key"
          :label="item.value"
          :value="item.key"
          class="search-select-option"
        />
      </el-select>
      <img
        class="icon-search cursor_p"
        @click="onSearch"
        src="@/assets/images/user/icon-search.png"
      />
    </div>
    <div class="flowRecord-list">
      <template v-for="(item, index) in list" :key="item.id">
        <div class="flowRecord-item-month" v-if="index == 0 || (index != list.length -1 && formatDate(list[index-1].createDatetime, 'yyyy年MM月') != formatDate(item.createDatetime, 'yyyy年MM月'))">
          <div class="icon"></div>
          <div class="title">{{ formatDate(item.createDatetime, 'yyyy年MM月') }}</div>
          <div class="balance">
            {{ index == 0 ? $t('Current balance') : $t('Closing balance') }}:
            {{ formatPrice(item.postAmount) }} FNXAI
          </div>
        </div>
        <div
          class="flowRecord-item cursor_p"
          @click="showFlowDetails(item)"
        >
          <!--   type  {0:每日奖励,1:邀请奖励, 2:提币, 3股东分红}     -->
          <img
            v-if="item.type === '2' || item.type === '6'"
            class="icon"
            src="@/assets/images/user/icon-out.png"
          />
          <img
            v-else-if="item.type === '8'"
            class="icon icon-transfer"
            src="@/assets/images/user/round_transfer.png"
          />
          <img v-else class="icon" src="@/assets/images/user/icon-in.png" />
          <div class="content">
            <div class="txt1">{{ typeList[item.type] }}</div>
            <div class="txt2">{{ formatDate(item.createDatetime, 'yyyy年MM月dd日') }}</div>
          </div>
          <div class="value-content">
            <div class="value">{{ formatPrice(item.amount) }} FNXAI</div>
            <div
              v-if="
                item.frozenAmount > 0 &&
                (item.type === '0' || item.type === '1' || item.type === '3' || item.type === '5')
              "
              class="value-extra"
            >
              {{ $t('Restake Amount') }}: {{ formatPrice(item.frozenAmount) }} FNXAI
            </div>
          </div>
        </div>
      </template>
      <div class="no_record" v-if="noData">
        <img src="@/assets/images/no_record.png" />
        <div class="txt">{{ $t('No records') }}</div>
      </div>
      <div class="more-w" v-if="!loading && !noData && !isEnd">
        <div class="more" @click="onLoadMore">{{ $t('Load more') }}</div>
      </div>
    </div>

    <DrawerAccountIncomeDetails
      :visible="drawerFlowDetailsVisible"
      :id="selectData?.id"
      @onChangeVisible="onChangeDrawerFlowDetailsVisible"
    />
  </div>
</template>

<style>
.accountIncomeRecord-container {
  padding-right: 15px;
  padding-top: 4px;
  flex: 1;
  .accountIncomeRecord-top-wrap {
    width: 100%;
    height: 125px;
    position: relative;
    .icon-left {
      width: 35px;
      position: absolute;
      top: 7px;
      left: 7px;
      z-index: 10;
    }
    .accountIncomeRecord-top {
      width: 100%;
      height: 125px;
      background-image: url('@/assets/images/user/blanace_bg.png');
      background-size: 100% 100%;
      position: relative;
      z-index: 9;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 20px;
      .item {
        width: 50%;
        height: 125px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        .icon {
          height: 24px;
        }
        .txt-w {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          margin-top: 9px;
        }
        .txt1 {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          line-height: 17px;
        }
        .txt2 {
          font-size: 17px;
          color: #ffffff;
          line-height: 24px;
          margin-top: 4px;
        }
      }
    }
  }
  .search-select-wraper {
    width: 100%;
    height: 36px;
    margin-top: 12px;
    background: rgba(50, 56, 68, 0.94);
    border-radius: 6px;
    position: relative;
    .search-select {
      font-size: 12px;
      color: #ffffff;
      flex: 1;
      .el-popper {
        max-width: 100%;
      }
    }
    .el-select--large .el-select__wrapper {
      min-height: 36px;
      padding: 0 30px 0 12px;
    }
    .icon-search {
      width: 19px;
      flex-shrink: 0;
      margin-left: 5px;
      position: absolute;
      top: 50%;
      right: 7px;
      transform: translateY(-50%);
    }
  }
  .more-w{
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30px 0 15px;
    .more{
      width: fit-content;
      border-radius: 6px;
      font-size: 15px;
      color: #13e6bc;
      text-align: center;
      flex-shrink: 0;
      padding: 3px 15px;
      cursor: pointer;
    }
  }
}
@media screen and (min-width: 1024px) {
  .accountIncomeRecord-container {
    padding-right: 0;
    .accountIncomeRecord-top-wrap {
      height: 95px;
      position: relative;
      .icon-left {
        width: 36px;
        top: 0;
        left: 0;
      }
      .accountIncomeRecord-top {
        width: calc(100% - 48px);
        height: 95px;
        background-image: url('@/assets/images/user/web_blanace_bg.png');
        margin-left: 48px;
        align-items: start;
        .item {
          height: 95px;
          .icon {
            height: 30px;
          }
          .txt-w {
            flex-direction: row;
            margin-top: 8px;
          }
          .txt1 {
            font-size: 16px;
            color: #ffffff;
            line-height: 22px;
          }
          .txt2 {
            font-size: 16px;
            line-height: 22px;
            margin-top: 0;
            margin-left: 5px;
          }
        }
      }
    }
    .search-select-wraper {
      width: 304px;
      height: 37px;
      margin-left: 8px;
      border-radius: 7px;
      padding: 0 7px 0 12px;
      margin-top: 29px;
      .search-select {
        font-size: 16px;
      }
      .icon-search {
        width: 22px;
      }
    }
    .more-w{
      padding: 40px 0 20px;
      .more{
        border-radius: 6px;
        font-size: 15px;
        padding: 3px 15px;
      }
    }
  }
}
</style>
