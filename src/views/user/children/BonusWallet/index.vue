<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { getAccount, switchChain } from '@wagmi/core'
import { CHAINS_CONFIG } from '@/config/index.js'
import DrawerSendFrom from '@/views/user/components/DrawerSendFrom/index.vue'
import DrawerAccountIncomeDetails from '@/views/user/components/DrawerAccountIncomeDetails/index.vue'
import { getMyAccount, getPageAccountIncome } from '@/api/user.js'
import { formatDate, formatPrice, isLogin, isMobileWeb } from '@/utils/index.js'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/user'
import StakeModal from '@/views/home/<USER>/StakeModal.vue'
import DrawerTransferFrom from '@/views/user/components/DrawerTransferFrom/index.vue'

const userStore = useUserStore()
const router = useRouter()
const { t } = useI18n()

// {0:每日奖励,1:邀请奖励, 2:提币, 3股东分红, 5:卓越奖励, 6:复投, 8:转账}
const typeList = {
  0: t('Daily Bonus'),
  1: t('Referral Bonus'),
  2: t('WithdrawDict'),
  3: t('Shareholder Bonus'),
  5: t('Extra Bonus'),
  6: t('Restake'),
  8: t('transfer')
}

const drawerSendFromVisible = ref(false)
const drawerFlowDetailsVisible = ref(false)
const accountInfo = ref()
const list = ref([])
const selectData = ref()
const noData = ref(false)

const goMore = () => {
  router.push({
    name: 'accountIncomeRecord',
    query: {
      active: 'bonusWallet'
    }
  })
}

const userInfo = ref()
const getInit = async () => {
  if (isLogin()) {
    try {
      noData.value = false
      const res = await getMyAccount()
      const resList = await getPageAccountIncome()
      const info = await userStore.getUserMsg()
      userInfo.value = info
      noData.value = resList.list.length <= 0
      accountInfo.value = res
      list.value = resList.list
    } catch (e) {
      //
    }
  }
}

const isLockAccount = computed(() => {
  return userStore.user.accountStatus == '2'
})
const restakeAmount = computed(() => {
  if (!accountInfo.value) {
    return 0
  }
  return accountInfo.value.settleAmount
})
const stakeVisible = ref(false)
const showStakeModal = () => {
  if (isMobileWeb()) {
    router.push({
      name: 'stake',
      query: {
        type: 'reinvest',
        accountType: '1'
      }
    })
  } else {
    stakeVisible.value = true
  }
}
const stakeSuccess = async () => {
  stakeVisible.value = false
  const res = await getMyAccount()
  accountInfo.value = res
}

const drawerTransferFromVisible = ref(false)
const onChangeDrawerTransferFromVisible = (v) => {
  drawerTransferFromVisible.value = v
}
const onTransferSuccess = () => {
  onChangeDrawerTransferFromVisible(false)
  getInit()
}

const onChangeDrawerSendFromVisible = (v) => {
  drawerSendFromVisible.value = v
}

const showTransferForm = () => {
  if (isMobileWeb()) {
    router.push({
      name: 'transfer',
      query: {
        accountType: '1'
      }
    })
  } else {
    drawerTransferFromVisible.value = true
  }
}

const showSendFrom = async () => {
  if (!userInfo.value || !userInfo.value.withdrawChainId) {
    return
  }
  const withdrawChainId = Number(userInfo.value.withdrawChainId)
  const { chainId } = getAccount(CHAINS_CONFIG)
  const chains = CHAINS_CONFIG.chains
  const chain = chains.find((chain) => chain.id === chainId)
  if (!chain || chain.unsupported || chain.id != withdrawChainId) {
    await switchChain(CHAINS_CONFIG, {
      chainId: withdrawChainId
    })
  }
  onChangeDrawerSendFromVisible(true)
}

const onSendSuccess = () => {
  onChangeDrawerSendFromVisible(false)
  getInit()
}

const onChangeDrawerFlowDetailsVisible = (v) => {
  drawerFlowDetailsVisible.value = v
}

const showFlowDetails = (item) => {
  selectData.value = item
  onChangeDrawerFlowDetailsVisible(true)
}

onMounted(async () => {
  getInit()
})
</script>

<template>
  <div class="availableBlanace-container">
    <div class="availableBlanace-top-wrap">
      <div class="availableBlanace-top">
        <div class="availableBlanace-top-l">
          <div class="txt1">{{ $t('Total Asset') }}:</div>
          <div class="txt2">
            <div class="txt">{{ formatPrice(accountInfo?.amount ?? 0) }} FNXAI</div>
            <div class="btn-wrap">
              <div class="btn-send cursor_p" @click="showStakeModal">{{ $t('Restake') }}</div>
              <div
                v-if="!isLockAccount"
                class="btn-send btn-web cursor_p"
                @click="showTransferForm"
              >
                {{ $t('transfer') }}
              </div>
              <div
                v-if="!isLockAccount"
                class="btn-mobile mobile-transfer-btn cursor_p"
                @click="showTransferForm"
              >
                <img src="@/assets/images/user/icon-transfer.png" />
              </div>
            </div>
          </div>
        </div>
        <div class="availableBlanace-top-r">
          <div class="availableBlanace-item has-after">
            <div class="txt1">{{ $t('Pending Settlement') }}:</div>
            <div class="txt2">{{ formatPrice(accountInfo?.toSettleAmount ?? 0) }} FNXAI</div>
          </div>
          <div class="availableBlanace-item has-after-web">
            <div class="txt1">{{ $t('Transferable Balance') }}:</div>
            <div class="txt2">{{ formatPrice(accountInfo?.settleAmount ?? 0) }} FNXAI</div>
          </div>
          <div class="availableBlanace-item">
            <div class="txt1">{{ $t('Withdraw Amount') }}</div>
            <div class="txt2">{{ formatPrice(accountInfo?.lockAmount) }} FNXAI
              <el-tooltip :content="$t('Send')" placement="bottom">
                <img class="icon_arrow_right cursor_p" src="@/assets/images/icon_send.png" @click="showSendFrom"/>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="availableBlanace-top-b">
          <div class="availableBlanace-item">
            <div>
              <div class="txt1">
                {{ $t('Withdraw Amount') }}
              </div>
              <div class="txt2">{{ formatPrice(accountInfo?.lockAmount ?? 0) }} FNXAI</div>
            </div>
            <div class="btn-restake cursor_p" @click="showSendFrom">{{ $t('Send') }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="transactions-title">
      <div class="title">{{ $t('Transactions') }}</div>
      <div class="more cursor_p" @click="goMore">
        <div class="txt">{{ $t('More') }}</div>
        <img class="icon-right" src="@/assets/images/arrow_right.png" />
      </div>
    </div>
    <div class="flowRecord-list">
      <div
        class="flowRecord-item cursor_p"
        v-for="item in list"
        :key="item.id"
        @click="showFlowDetails(item)"
      >
        <!--   type  {0:每日奖励,1:邀请奖励, 2:提币, 3股东分红, 5:卓越奖励, 6:复投, 8:转账}     -->
        <img
          v-if="item.type === '2' || item.type === '6'"
          class="icon"
          src="@/assets/images/user/icon-out.png"
        />
        <img
          v-else-if="item.type === '8'"
          class="icon icon-transfer"
          src="@/assets/images/user/round_transfer.png"
        />
        <img v-else class="icon" src="@/assets/images/user/icon-in.png" />
        <div class="content">
          <div class="txt1">{{ typeList[item.type] }}</div>
          <div class="txt2">{{ formatDate(item.createDatetime, 'yyyy年MM月dd日') }}</div>
        </div>
        <div class="value-content">
          <div class="value">{{ formatPrice(item.amount) }} FNXAI</div>
          <div
            v-if="
              item.frozenAmount > 0 &&
              (item.type === '0' || item.type === '1' || item.type === '3' || item.type === '5')
            "
            class="value-extra"
          >
            {{ $t('Restake Amount') }}: {{ formatPrice(item.frozenAmount) }} FNXAI
          </div>
        </div>
      </div>
      <div class="no_record" v-if="noData">
        <img src="@/assets/images/no_record.png" />
        <div class="txt">{{ $t('No records') }}</div>
      </div>
    </div>
    <stake-modal
      type="reinvest"
      accountType="1"
      :visible="stakeVisible"
      :availableBalance="restakeAmount"
      @cancel="stakeVisible = false"
      @success="stakeSuccess"
    />
    <DrawerAccountIncomeDetails
      :visible="drawerFlowDetailsVisible"
      :id="selectData?.id"
      @onChangeVisible="onChangeDrawerFlowDetailsVisible"
    />
    <DrawerSendFrom
      :visible="drawerSendFromVisible"
      :type="'1'"
      @onChangeVisible="onChangeDrawerSendFromVisible"
      @onSuccess="onSendSuccess"
    />
    <DrawerTransferFrom
      :visible="drawerTransferFromVisible"
      :type="'1'"
      @onChangeVisible="onChangeDrawerTransferFromVisible"
      @onSuccess="onTransferSuccess"
    />
  </div>
</template>

<style scoped>
.availableBlanace-container {
  padding-right: 15px;
  padding-top: 4px;
  .availableBlanace-top-wrap {
    width: 100%;
    position: relative;
    .mobile-transfer-btn {
      width: 25px;
      height: 23.5px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .btn-web {
      display: none;
    }
    .btn-mobile {
      display: block;
    }
    .availableBlanace-top {
      width: 100%;
      background-image: url('@/assets/images/user/blanace_bg.png');
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      padding: 0 19px;

      .availableBlanace-top-l {
        width: 100%;
        position: relative;
        margin-top: 17px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding-bottom: 9px;
        .txt1 {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          line-height: 17px;
        }
        .txt2 {
          font-size: 17px;
          color: #ffffff;
          line-height: 24px;
          display: flex;
          align-items: start;
          justify-content: space-between;
          .txt {
            margin-top: 4px;
          }
        }
        .btn-wrap {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 10px;
        }
      }
      .availableBlanace-top-r {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-top: 9px;
        padding-bottom: 9px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        .availableBlanace-item {
          width: 100%;
          position: relative;
          &.lock-item {
            display: none;
          }
          &.has-after {
            margin-right: 15px;
            &:after {
              content: '';
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 1px;
              height: 28px;
              background-color: rgba(255, 255, 255, 0.2);
            }
          }
        }
        .txt1 {
          font-size: 10px;
          color: rgba(255, 255, 255, 0.7);
          line-height: 14px;
        }
        .txt2 {
          font-size: 12px;
          color: #ffffff;
          line-height: 16px;
          margin-top: 4px;
        }
      }
      .availableBlanace-top-b {
        padding-top: 9px;
        padding-bottom: 15px;
        .availableBlanace-item {
          display: flex;
          align-items: flex-end;
          justify-content: space-between;
          width: 100%;
          position: relative;
        }
        .txt1 {
          font-size: 10px;
          color: rgba(255, 255, 255, 0.7);
          line-height: 14px;
        }
        .txt2 {
          font-size: 12px;
          color: #ffffff;
          line-height: 16px;
          margin-top: 4px;
        }
      }

      .btn-send {
        min-width: 57px;
        padding: 0 10px;
        background: #13e6bc;
        border-radius: 6px;
        font-size: 13px;
        color: #ffffff;
        line-height: 23px;
        text-align: center;
        margin-left: 5px;
        flex-shrink: 0;
        & + .btn-send {
          margin-left: 0;
        }
      }
      .btn-restake {
        display: inline-flex;
        align-items: center;
        margin-bottom: 2px;
        font-size: 12px;
        color: #ffffff;
        height: 20px;
        text-align: center;
        padding: 0 8px;
        border-radius: 4px;
        background-color: #13e6bc;
      }
    }
  }
  .transactions-title {
    display: flex;
    margin-top: 21px;
    justify-content: space-between;
    .title {
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
    }
    .more {
      display: flex;
      .txt {
        font-size: 12px;
        color: #ffffff;
        line-height: 17px;
      }
      .icon-right {
        width: 17px;
      }
    }
  }
  .icon_arrow_right{
    height: 16px;
    margin-left: 3px;
  }
}
@media screen and (min-width: 1024px) {
  .availableBlanace-container {
    padding-right: 0;
    .availableBlanace-top-b {
      display: none;
    }
    .availableBlanace-top-wrap {
      height: 95px;
      position: relative;
      .availableBlanace-top {
        height: 95px;
        background-image: url('@/assets/images/user/web_blanace_bg.png');
        align-items: center;
        padding: 0 25px;
        flex-direction: row;
        .availableBlanace-top-l {
          border-bottom: none;
          margin-top: 0;
          padding-bottom: 0;
          .txt1 {
            font-size: 14px;
            line-height: 20px;
          }
          .txt2 {
            font-size: 20px;
            line-height: 28px;
            justify-content: start;
            align-items: center;
            margin-top: 7px;
            .txt {
              margin-top: 0;
            }
          }
          .btn-send {
            margin-left: 10px;
            & + .btn-send {
              margin-left: 0;
            }
          }
        }
        .availableBlanace-top-r {
          justify-content: end;
          margin-top: 5px;
          padding-bottom: 0;
          border-bottom: none;
          .availableBlanace-item {
            width: 176px;
            text-align: center;
            &.has-after,
            &.has-after-web {
              margin-right: 0;
              &:after {
                height: 33px;
              }
            }
            &.has-after-web {
              &:after {
                content: '';
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 1px;
                height: 28px;
                background-color: rgba(255, 255, 255, 0.2);
              }
            }
          }
          .txt1 {
            font-size: 13px;
            line-height: 18px;
            span {
              font-size: 14px;
              line-height: 20px;
              color: #ffffff;
              font-weight: 600;
              padding-left: 17px;
            }
          }
          .txt2 {
            font-size: 14px;
            line-height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
      .btn-mobile {
        display: none;
      }
      .btn-web {
        display: block;
      }
      .btn-restake {
        margin-top: 6px;
        margin-bottom: 0;
        height: 16px;
        padding: 0 13px;
      }
    }
    .transactions-title {
      margin-top: 23px;
      .title {
        font-size: 16px;
        line-height: 22px;
      }
      .more {
        display: flex;
        .txt {
          font-size: 14px;
          line-height: 20px;
        }
        .icon-right {
          width: 18px;
        }
      }
    }
  }
  .icon_arrow_right{
    height: 16px;
  }
}
</style>
