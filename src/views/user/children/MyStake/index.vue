<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { getMyAccount, getPageProductOrder, getProductOrderSumAmount } from '@/api/user'
import CustomTable from '@/components/CustomTable/index.vue'
import DrawerNextDisburesmentDate from '../../components/DrawerNextDisburesmentDate/index.vue'
import { CHAINS_CONFIG } from '@/config'
import { getAccount, watchAccount, switchChain } from '@wagmi/core'
import { formatDate, formatPrice, isLogin, isMobileWeb, isPhone } from '@/utils'
import { useI18n } from 'vue-i18n'
import { getProductAccountInfo } from '@/api/stake'
import { useUserStore } from '@/stores/user'
import StakeModal from '@/views/home/<USER>/StakeModal.vue'
import DrawerSendFrom from '@/views/user/components/DrawerSendFrom/index.vue'
import DrawerAccountIncomeDetails from '@/views/user/components/DrawerAccountIncomeDetails/index.vue'
import DrawerTransferFrom from '@/views/user/components/DrawerTransferFrom/index.vue'

const { locale, t } = useI18n()
const router = useRouter()
const userStore = useUserStore()

const statusList = [
  {
    key: '0',
    value: t('On-going')
  },
  {
    key: '1',
    value: t('Completed')
  },
  {
    key: 'all',
    value: t('ALL')
  }
]

const childRef = ref()
const status = ref('all')
const currentChainId = ref('')
const productOrderSum = ref()
const drawerNextDisburesmentDateVisible = ref(false)
const selectRow = ref()
const userInfo = ref()
const accountInfo = ref()

const columns = computed(() => [
  // {
  //   titleKey: 'type',
  //   dataIndex: 'accountType',
  //   width: 80,
  //   render: (v) => (v == '0' ? t('Stake') : t('Reinvest'))
  // },
  {
    titleKey: 'S/N',
    dataIndex: 'id',
    width: 95,
    render: (v) => v.slice(0, 9) + ' ' + v.slice(9)
  },
  {
    titleKey: 'Staked Time',
    dataIndex: 'buyTime',
    width: isPhone() ? 100 : 150
  },
  {
    titleKey: 'Staked Amount (FNXAI)',
    dataIndex: 'amount',
    width: isPhone() ? 110 : 130,
    render: (v) => formatPrice(v)
  },
  {
    titleKey: 'dailyInterestAmount',
    dataIndex: 'dailyInterestAmount',
    width: isPhone() ? 110 : 120,
    render: (v) => formatPrice(v)
  },
  {
    titleKey: 'Completed Days',
    dataIndex: 'incomeDay',
    width: isPhone() ? 90 : null
  },
  {
    titleKey: 'Next Disbursement Date',
    dataIndex: 'nextIncomeTime',
    width: isPhone() ? 140 : null
  },
  {
    titleKey: 'Balance Payout',
    dataIndex: 'amountCanDelivery',
    width: isPhone() ? (locale.value == 'ko-KR' ? 110 : 120) : 120,
    render: (v) => formatPrice(v)
  },
  {
    titleKey: 'Status1',
    dataIndex: 'status',
    width: isPhone() ? (locale.value == 'ko-KR' ? 110 : 120) : 120,
  }
])

const onChangeDrawerNextDisburesmentDateVisible = (v) => {
  drawerNextDisburesmentDateVisible.value = v ?? false
}

const handleNextDisburesmentClick = (row) => {
  onChangeDrawerNextDisburesmentDateVisible(true)
  selectRow.value = row
}
const goBlanace = () => {
  router.push({
    name: 'availableBlanace',
    query: {
      active: 'myStake'
    }
  })
}
const goDividendsList = () => {
  router.push({
    name: 'dividendsList',
    query: {
      active: 'myStake'
    }
  })
}

const onSearch = () => {
  childRef.value.handleSearch()
}

const chains = CHAINS_CONFIG.chains

const getInit = async () => {
  const { chainId } = getAccount(CHAINS_CONFIG)
  const chain = chains.find((chain) => chain.id === chainId)
  if (chainId && !chain) {
    return
  }
  currentChainId.value = chainId

  childRef.value.hanldeReset()
  try {
    const account = await getMyAccount()
    const sum = await getProductAccountInfo({ chainId: chainId })
    const info = await userStore.getUserMsg()
    userInfo.value = info
    accountInfo.value = account
    productOrderSum.value = sum
  } catch (e) {
    //
  }
}

onMounted(async () => {
  if (isLogin()) {
    await getInit()
  }
})

const unwatchAccount = watchAccount(CHAINS_CONFIG, {
  async onChange(network, prevNetwork) {
    if (network.chainId != prevNetwork.chainId) {
      await getInit(network.chainId)
    }
  }
})

onUnmounted(() => {
  unwatchAccount()
})


const drawerSendFromVisible = ref(false)
const drawerFlowDetailsVisible = ref(false)

const isLockAccount = computed(() => {
  return userStore.user.accountStatus == '2'
})
const restakeAmount = computed(() => {
  if (!accountInfo.value || !userStore.user) {
    return 0
  }
  return productOrderSum.value.availableBalance
})
const stakeVisible = ref(false)
const showStakeModal = () => {
  if (isMobileWeb()) {
    router.push({
      name: 'stake',
      query: {
        type: 'reinvest',
        accountType: '0'
      }
    })
  } else {
    stakeVisible.value = true
  }
}
const stakeSuccess = async () => {
  stakeVisible.value = false
  getInit()
}


const drawerTransferFromVisible = ref(false)
const onChangeDrawerTransferFromVisible = (v) => {
  drawerTransferFromVisible.value = v
}
const onTransferSuccess = () => {
  onChangeDrawerTransferFromVisible(false)
  getInit()
}

const onChangeDrawerSendFromVisible = (v) => {
  drawerSendFromVisible.value = v
}

const showTransferForm = () => {
  if (isMobileWeb()) {
    router.push({
      name: 'transfer',
      query: {
        accountType: '0'
      }
    })
  } else {
    drawerTransferFromVisible.value = true
  }
}

const showSendFrom = async () => {
  if (!userInfo.value || !userInfo.value.withdrawChainId) {
    return
  }
  const withdrawChainId = Number(userInfo.value.withdrawChainId)
  const { chainId } = getAccount(CHAINS_CONFIG)
  const chains = CHAINS_CONFIG.chains
  const chain = chains.find((chain) => chain.id === chainId)
  if (!chain || chain.unsupported || chain.id != withdrawChainId) {
    await switchChain(CHAINS_CONFIG, {
      chainId: withdrawChainId
    })
  }
  onChangeDrawerSendFromVisible(true)
}

const onSendSuccess = () => {
  onChangeDrawerSendFromVisible(false)
  getInit()
}

const onChangeDrawerFlowDetailsVisible = (v) => {
  drawerFlowDetailsVisible.value = v
}

</script>

<template>
  <div class="myStake-container">
    <div class="myStake-top-wrap">
      <div class="myStake-top">
        <div class="myStake-top-l">
          <div class="txt1">{{ $t('Available Balance') }}<img class="icon_arrow_right cursor_p" src="@/assets/images/icon_arrow_right.png" @click="goBlanace"/></div>
          <div class="txt2">
            <div class="txt">{{ formatPrice(productOrderSum?.availableBalance) }} FNXAI</div>
            <div class="btn-wrap">
              <div class="btn-send cursor_p" @click="showStakeModal">{{ $t('Restake') }}</div>
              <div
                v-if="!isLockAccount"
                class="btn-send btn-web cursor_p"
                @click="showTransferForm"
              >
                {{ $t('transfer') }}
              </div>
              <div
                v-if="!isLockAccount"
                class="btn-mobile mobile-transfer-btn cursor_p"
                @click="showTransferForm"
              >
                <img src="@/assets/images/user/icon-transfer.png" />
              </div>
            </div>
          </div>
        </div>
        <div class="myStake-top-r">
          <div class="availableBlanace-item has-after">
            <div class="txt1">{{ $t('Payout Received') }}</div>
            <div class="txt2">{{ formatPrice(productOrderSum?.received) }} FNXAI</div>
          </div>
          <div class="availableBlanace-item has-after-web">
            <div class="txt1">{{ $t('Payout Balance') }}</div>
            <div class="txt2">{{ formatPrice(productOrderSum?.balance) }} FNXAI</div>
          </div>

          <div class="availableBlanace-item has-after-web dividends-item">
            <div class="txt1">{{ $t('Dividends') }}<img class="icon_arrow_right cursor_p" src="@/assets/images/icon_arrow_right.png" @click="goDividendsList"/></div>
            <div class="txt2">{{ formatPrice(productOrderSum?.rewardTotal) }} FNXAI</div>
          </div>
          <div class="availableBlanace-item lock-item">
            <div class="txt1">{{ $t('Withdraw Amount') }}</div>
            <div class="txt2">{{ formatPrice(productOrderSum?.withdrawIncome) }} FNXAI
              <el-tooltip :content="$t('Send')" placement="bottom">
                <img class="icon_arrow_right cursor_p" src="@/assets/images/icon_send.png" @click="showSendFrom"/>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="myStake-top-b">
          <div class="availableBlanace-item dividends-item">
            <div>
              <div class="txt1">
                {{ $t('Dividends') }}
              </div>
              <div class="txt2">{{ formatPrice(productOrderSum?.rewardTotal ?? 0) }} FNXAI</div>
            </div>
            <img class="icon_arrow_right cursor_p" src="@/assets/images/icon_arrow_right.png" @click="goDividendsList"/>
          </div>
          <div class="availableBlanace-item">
            <div>
              <div class="txt1">
                {{ $t('Withdraw Amount') }}
              </div>
              <div class="txt2">{{ formatPrice(productOrderSum?.withdrawIncome ?? 0) }} FNXAI</div>
            </div>
            <div class="btn-restake cursor_p" @click="showSendFrom">{{ $t('Send') }}</div>
          </div>
        </div>
      </div>

      <div class="myStake-search">
        <div class="myStake-search-label">{{ $t('Status') }}:</div>
        <div class="search-select-wraper">
          <el-select
            v-model="status"
            :placeholder="$t('Select')"
            size="large"
            class="search-select"
            effect="dark"
            placement="bottom"
            :teleported="false"
          >
            <el-option
              v-for="item in statusList"
              :key="item.key"
              :label="item.value"
              :value="item.key"
              class="search-select-option"
            />
          </el-select>
          <img
            class="icon-search cursor_p"
            @click="onSearch"
            src="@/assets/images/user/icon-search.png"
          />
        </div>
      </div>
    </div>
    <div class="table-contaner">
      <CustomTable
        ref="childRef"
        :columns="columns"
        :search-params="{ status: status !== 'all' ? status : '', chainId: currentChainId }"
        :get-page-list="getPageProductOrder"
        :stripe="true"
        :noInit="true"
      >
        <template #buyTime="{ value }">
          <div>{{ formatDate(value, 'dd/MM/yyyy') }}</div>
          <div>{{ formatDate(value, 'hh:mm:ss') }}</div>
        </template>
        <template #incomeDay="{ data, value }">
          <!-- 状态{0:质押中,1:已到期} -->
          <div class="c_13E6BC" v-if="data.status == '1'">{{ $t('Completed') }}</div>
          <div>{{ value }}/{{ data.lockTime }}</div>
        </template>

        <template #nextIncomeTime="{ data, value }">
          <!-- 状态{0:质押中,1:已到期,2质押失败} -->
          <div class="c_13E6BC" v-if="data.status == '1'">
            {{ $t('Completed') }}
          </div>
          <div class="c_error" v-if="data.status == '2'">
            {{ data.errorMsg ?? $t('Completed') }}
          </div>
          <div
            class="t_cursor_p"
            v-if="data.status == '0' && data.productType != '2'"
            @click="handleNextDisburesmentClick(data)"
          >
            {{ formatDate(value, 'dd/MM/yyyy') }}
          </div>
          <div v-else>
            {{ formatDate(value, 'dd/MM/yyyy') }}
          </div>
        </template>
        <template #status="{ data }">
          <div class="icon_star_wrap" v-if="data.vipFlag == '1'">
            <template v-for="item in data.vipLevel" :key="item">
              <img src="@/assets/images/icon_star.png"/>
            </template>
          </div>
          <div class="icon_wrap">
            <img v-if="data.stockFlag == '1'" src="@/assets/images/icon_gupiao.png"/>
            <img v-if="data.vipFlag == '1'" src="@/assets/images/icon_qiandaizi.png"/>
          </div>
        </template>
      </CustomTable>
    </div>
    <DrawerNextDisburesmentDate
      :visible="drawerNextDisburesmentDateVisible"
      :productOrderId="selectRow?.id"
      @onChangeVisible="onChangeDrawerNextDisburesmentDateVisible"
    />

    <stake-modal
      type="reinvest"
      accountType="0"
      :visible="stakeVisible"
      :availableBalance="restakeAmount"
      @cancel="stakeVisible = false"
      @success="stakeSuccess"
    />
    <DrawerAccountIncomeDetails
      :visible="drawerFlowDetailsVisible"
      :id="selectData?.id"
      @onChangeVisible="onChangeDrawerFlowDetailsVisible"
    />
    <DrawerSendFrom
      :visible="drawerSendFromVisible"
      :type="'0'"
      @onChangeVisible="onChangeDrawerSendFromVisible"
      @onSuccess="onSendSuccess"
    />
    <DrawerTransferFrom
      :visible="drawerTransferFromVisible"
      :type="'0'"
      @onChangeVisible="onChangeDrawerTransferFromVisible"
      @onSuccess="onTransferSuccess"
    />
  </div>
</template>

<style>
.myStake-container {
  padding-top: 4px;

  .myStake-top-wrap {
    width: 100%;
    position: relative;
    margin-bottom: 12px;
    padding-right: 15px;

    .mobile-transfer-btn {
      width: 25px;
      height: 23.5px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .btn-web {
      display: none;
    }
    .btn-mobile {
      display: block;
    }
    .myStake-top {
      width: 100%;
      background-image: url('@/assets/images/user/blanace_bg.png');
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      padding: 0 19px;

      .myStake-top-l {
        width: 100%;
        position: relative;
        margin-top: 17px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        padding-bottom: 9px;
        .txt1 {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          line-height: 17px;
          display: flex;
          align-items: center;
        }
        .txt2 {
          font-size: 17px;
          color: #ffffff;
          line-height: 24px;
          display: flex;
          align-items: start;
          justify-content: space-between;
          .txt {
            margin-top: 4px;
          }
        }
        .btn-wrap {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 10px;
        }
      }
      .myStake-top-r {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-top: 9px;
        padding-bottom: 9px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        .availableBlanace-item {
          width: 100%;
          position: relative;
          &.dividends-item{
            display: none;
          }
          &.lock-item {
            display: none;
          }
          &.has-after {
            margin-right: 15px;
            &:after {
              content: '';
              position: absolute;
              right: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 1px;
              height: 28px;
              background-color: rgba(255, 255, 255, 0.2);
            }
          }
        }
        .txt1 {
          font-size: 10px;
          color: rgba(255, 255, 255, 0.7);
          line-height: 14px;
        }
        .txt2 {
          font-size: 12px;
          color: #ffffff;
          line-height: 16px;
          margin-top: 4px;
          white-space: nowrap;
        }
      }
      .myStake-top-b {
        padding-top: 9px;
        padding-bottom: 15px;
        .availableBlanace-item {
          display: flex;
          align-items: flex-end;
          justify-content: space-between;
          width: 100%;
          position: relative;
          &.dividends-item{
            padding-bottom: 9px;
            margin-bottom: 9px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            align-items: center;
          }
        }
        .txt1 {
          font-size: 10px;
          color: rgba(255, 255, 255, 0.7);
          line-height: 14px;
        }
        .txt2 {
          font-size: 12px;
          color: #ffffff;
          line-height: 16px;
          margin-top: 4px;
        }
      }

      .btn-send {
        min-width: 57px;
        padding: 0 10px;
        background: #13e6bc;
        border-radius: 6px;
        font-size: 13px;
        color: #ffffff;
        line-height: 23px;
        text-align: center;
        margin-left: 5px;
        flex-shrink: 0;
        & + .btn-send {
          margin-left: 0;
        }
      }
      .btn-restake {
        display: inline-flex;
        align-items: center;
        margin-bottom: 2px;
        font-size: 12px;
        color: #ffffff;
        height: 20px;
        text-align: center;
        padding: 0 8px;
        border-radius: 4px;
        background-color: #13e6bc;
      }
    }

    .myStake-search {
      display: flex;
      align-items: center;
      margin-top: 12px;
      .myStake-search-label {
        font-size: 12px;
        color: #ffffff;
        display: none;
        margin-right: 7px;
      }
      .search-select-wraper {
        width: 100%;
        height: 40px;
        background: rgba(50, 56, 68, 0.94);
        border-radius: 6px;
        position: relative;
        .search-select {
          font-size: 12px;
          color: #ffffff;
          flex: 1;
          .el-popper {
            max-width: 100%;
          }
        }
        .el-select--large .el-select__wrapper {
          min-height: 40px;
          padding: 0 30px 0 12px;
        }
        .icon-search {
          width: 19px;
          flex-shrink: 0;
          margin-left: 5px;
          position: absolute;
          top: 50%;
          right: 7px;
          transform: translateY(-50%);
        }
      }
    }
  }
  .icon_arrow_right{
    height: 16px;
    margin-left: 3px;
  }
  .icon_wrap{
    display: flex;
    align-items: center;
    justify-content: center;
    img{
      height: 18px;
    }
  }
  .icon_star_wrap{
    display: flex;
    align-items: center;
    justify-content: center;
    img{
      height: 10px;
    }
  }
}
@media (min-width: 1024px) {
  .myStake-container {
    .myStake-top-b {
      display: none;
    }
    .myStake-top-wrap {
      position: relative;
      padding-right: 0;
      margin-bottom: 27px;
      .myStake-top {
        height: 95px;
        background-image: url('@/assets/images/user/web_blanace_bg.png');
        align-items: center;
        padding: 0 25px;
        flex-direction: row;
        .myStake-top-l {
          border-bottom: none;
          margin-top: 0;
          padding-bottom: 0;
          .txt1 {
            font-size: 14px;
            line-height: 20px;
          }
          .txt2 {
            font-size: 20px;
            line-height: 28px;
            justify-content: start;
            align-items: center;
            margin-top: 7px;
            .txt {
              margin-top: 0;
            }
          }
          .btn-send {
            margin-left: 10px;
            & + .btn-send {
              margin-left: 0;
            }
          }
        }
        .myStake-top-r {
          justify-content: end;
          margin-top: 5px;
          padding-bottom: 0;
          border-bottom: none;
          .availableBlanace-item {
            width: 154px;
            text-align: center;
            &.dividends-item{
              display: flex;
              flex-direction: column;
              align-items: center;
            }
            &.lock-item {
              width: auto;
              display: flex;
              flex-direction: column;
              align-items: center;
              padding-left: 15px;
              .txt1{
                white-space: nowrap;
              }
            }
            &.has-after,
            &.has-after-web {
              margin-right: 0;
              &:after {
                height: 33px;
              }
            }
            &.has-after-web {
              &:after {
                content: '';
                position: absolute;
                right: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 1px;
                height: 28px;
                background-color: rgba(255, 255, 255, 0.2);
              }
            }
          }
          .txt1 {
            font-size: 13px;
            line-height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            span {
              font-size: 14px;
              line-height: 20px;
              color: #ffffff;
              font-weight: 600;
              padding-left: 17px;
            }
          }
          .txt2 {
            width: 100%;
            font-size: 14px;
            line-height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }
      .btn-mobile {
        display: none;
      }
      .btn-web {
        display: block;
      }
      .btn-restake {
        margin-top: 6px;
        margin-bottom: 0;
        height: 16px;
        padding: 0 13px;
      }

      .myStake-search {
        margin-top: 29px;
        .myStake-search-label {
          display: block;
          font-size: 16px;
          line-height: 37px;
        }
        .search-select-wraper {
          width: 235px;
          height: 37px;
          margin-left: 8px;
          border-radius: 7px;
          padding: 0 7px 0 12px;
          .search-select {
            font-size: 16px;
          }
          .icon-search {
            width: 22px;
          }
        }
      }
    }

    .icon_arrow_right{
      height: 16px;
    }
    .icon_wrap{
      img{
        height: 24px;
      }
    }
    .icon_star_wrap{
      img{
        height: 14px;
      }
    }
  }
}
</style>
