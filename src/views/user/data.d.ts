export type AssociationItem = {
  id: string;
  userId: string;
  user: {
    loginName: string;
  };
  leftId: string;
  isLeader: string;
  nickName: string;
  address: string;
  createDatetime: number;
  level: number;
  tu: string;
  leftTeamUser?: AssociationItem;
  rightTeamUser?: AssociationItem;
  total: number;
  teamUserProduct: TeamUserProduct;
};

export type TeamUserProduct = {
  id: string;
  teamId: string;
  isLeader: string;
  waitLeftCount: number;
  waitLeftAmount: number;
  settleLeftCount: number;
  settleLeftAmount: number;
  waitRightCount: number;
  waitRightAmount: number;
  settleRightCount: number;
  settleRightAmount: number;
  createDatetime: number;
  productId: string;
  price: number;
};

export type EmptyBallItem = {
  id: string;
  address: string;
  newId: string;
  status: string;
  creatime: number;
  remark?: string;
  statusName: string;
};
