<script setup>
import { watch, ref } from 'vue'
import { formatPrice, isLogin, isPhone } from '@/utils'
import { getMyAccount, accountTransfer, productAccountTransfer } from '@/api/user.js'
import { getAccount } from '@wagmi/core'
import message from '@/utils/message.js'
import { useI18n } from 'vue-i18n'
import { getProductAccountInfo } from '@/api/stake.js'
import { CHAINS, CHAINS_CONFIG } from '@/config/index.js'

const { t } = useI18n()

const chains = CHAINS_CONFIG.chains
const cChainId = ref()

const propsData = defineProps({
  visible: Boolean,
  type: String // 0:静态划转，1:动态划转
})

const amount = ref('')
const show = ref(false)
const balance = ref('0')
const accountInfo = ref()
const sendLoading = ref(false)

const emits = defineEmits(['onChangeVisible', 'onSuccess'])

const onClose = () => {
  emits('onChangeVisible', false)
}

const getInit = async () => {
  if (isLogin()) {
    const { chainId } = getAccount(CHAINS_CONFIG)
    const chain = chains.find((chain) => chain.id === chainId)
    if (chainId && !chain) {
      cChainId.value = CHAINS[0].id
    } else {
      cChainId.value = chainId
    }
    try {
      // 0:静态划转，1:动态划转
      if(propsData.type == '0') {
        const res = await getProductAccountInfo({ chainId: chainId })
        balance.value = res.availableBalance
        accountInfo.value = res
      }
      if(propsData.type == '1') {
        const res = await getMyAccount()
        balance.value = res.settleAmount
        accountInfo.value = res
      }
    } catch (e) {
      //
    }
  }
}

watch(
  () => propsData.visible,
  () => {
    show.value = propsData.visible
    if (propsData.visible) {
      amount.value = ''
      getInit()
    }
  }
)

const onMax = () => {
  amount.value = formatPrice(balance.value)
}

const onTransfer0 = async () => {
  try {
    try {
      await productAccountTransfer({ chainId: cChainId.value, amount: amount.value })
      sendLoading.value = false
      message.success(t('Successful'))
      emits('onSuccess')
    } catch (error) {
      sendLoading.value = false
      return
    }
  } catch (error) {
    sendLoading.value = false
  }
}

const onTransfer1 = async () => {
  try {
    try {
      await accountTransfer(amount.value)
      sendLoading.value = false
      message.success(t('Successful'))
      emits('onSuccess')
    } catch (error) {
      sendLoading.value = false
      return
    }
  } catch (error) {
    sendLoading.value = false
  }
}

const onSubmit = async () => {
  if (sendLoading.value) {
    return
  }
  if (isNaN(amount.value) || amount.value <= 0) {
    message.warning(t('Please enter amount to transfer'))
    return
  }
  if (balance.value <= 0 || amount.value > balance.value) {
    message.warning(t('Available Balance is not enough'))
    return
  }
  sendLoading.value = true
  if (propsData.type === '0') {
    onTransfer0()
  }
  if (propsData.type === '1') {
    onTransfer1()
  }
}
</script>

<template>
  <el-drawer
    v-model="show"
    :direction="isPhone() ? 'rtl' : 'btt'"
    class="drawer-wrapper transfer-drawer"
    :destroy-on-close="true"
    :show-close="false"
    @close="onClose"
  >
    <template #header>
      <div class="drawer-header">
        <div class="title">{{ $t('transfer') }}</div>
        <img class="icon-close" src="@/assets/images/close_w.png" @click="onClose" />
      </div>
    </template>
    <template #default>
      <div class="transfer-container">
        <div class="transfer-top-container">
          <div class="transfer-top-container-l">
            <div class="l-dot l-dot-0"></div>
            <div class="l-dot"></div>
            <div class="l-dot"></div>
            <div class="l-dot"></div>
            <div class="l-dot l-dot-4"></div>
          </div>
          <div class="transfer-top-container-r">
            <div class="r-line">
              <label>{{ $t('From') }}</label>
              <span>{{ $t('Transferable Balance') }}</span>
            </div>
            <div class="r-line">
              <label>{{ $t('To') }}</label>
              <span>{{ $t('Restake Amount') }}</span>
            </div>
          </div>
        </div>
        <div class="transfer-title">{{ $t('Amount') }}</div>
        <div class="transfer-amount-box">
          <input
            type="text"
            :placeholder="$t('Please enter amount to transfer')"
            v-model="amount"
          />
          <div class="amount-line"></div>
          <div class="amount-btn" @click="onMax">{{ $t('Max') }}</div>
        </div>
        <div class="transfer-title">{{ $t('Available') }} {{ formatPrice(balance) }} FNXAI</div>
        <div class="btn-confirm cursor_p" @click="onSubmit" v-loading="sendLoading">
          {{ $t('transfer') }}
        </div>
      </div>
    </template>
  </el-drawer>
</template>

<style>
@media screen and (min-width: 1024px) {
  .transfer-drawer.el-drawer {
    width: 530px !important;
    height: 493px !important;
    border-radius: 15px;
    background-color: #171c22;
    background-image: none;
  }
}
.transfer-drawer {
  background-color: #192027;
  .drawer-header {
    position: relative;
    padding: 5px 15px 0 15px;
    .title {
      width: 100%;
      font-size: 17px;
      color: #ffffff;
      line-height: 24px;
      text-align: center;
      padding: 15px 0;
    }
    .icon-close {
      width: 22px;
      position: absolute;
      top: 19px;
      right: 12px;
    }
  }
  .transfer-container {
    padding-left: 28px;
    padding-right: 28px;
    padding-bottom: 38px;
    .transfer-top-container {
      padding-left: 15px;
      padding-right: 22px;
      background-color: rgba(50, 56, 68, 0.5);
      border-radius: 9.5px;
      position: relative;
      display: flex;
      align-items: center;
      gap: 17px;
      margin-bottom: 7px;
      .transfer-top-container-l {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 13px;
        .l-dot {
          width: 4.13px;
          height: 4.13px;
          border-radius: 50%;
          background-color: #2a5274;
        }
        .l-dot-0,
        .l-dot-4 {
          width: 6.88px;
          height: 6.88px;
        }
        .l-dot-0 {
          background-color: #e15167;
        }
        .l-dot-4 {
          background-color: #2bad6f;
        }
      }
      .transfer-top-container-r {
        flex: 1;
        .r-line {
          padding: 26px 0;
          display: flex;
          align-items: center;
          border-bottom: 1px solid #3e494d;
          &:last-child {
            border-bottom: none;
          }
          label {
            font-size: 14px;
            color: #607b92;
            line-height: 20px;
            flex-basis: 56px;
            text-align: left;
          }
          span {
            font-weight: 500;
            font-size: 14px;
            color: #ffffff;
            line-height: 20px;
          }
        }
      }
    }
    .transfer-title {
      padding-top: 23px;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
    }
    .transfer-amount-box {
      margin-top: 15px;
      padding: 23px 15px;
      background-color: rgba(50, 56, 68, 0.71);
      border: 1px solid #5a6164;
      border-radius: 15.83px;
      display: flex;
      align-items: center;
      gap: 15px;
      input {
        flex: 1;
        font-weight: 500;
        font-size: 14px;
        line-height: 25px;
        color: #ffffff;
        &::placeholder {
          color: #cfd2d6;
        }
      }
      .amount-line {
        width: 1px;
        height: 23px;
        background-color: #e1e1e1;
      }
      .amount-btn {
        padding-right: 17px;
        font-weight: 600;
        font-size: 14px;
        color: #ffffff;
        line-height: 20px;
      }
    }
    .btn-confirm {
      width: 100%;
      height: 43px;
      background: #13e6bc;
      border-radius: 6px;
      font-size: 14px;
      color: #ffffff;
      line-height: 43px;
      margin-top: 23px;
      text-align: center;
    }
  }
}
</style>
