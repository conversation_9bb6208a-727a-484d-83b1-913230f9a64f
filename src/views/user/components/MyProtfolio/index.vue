<script setup>
import { computed, ref, watch } from 'vue'
import { truncateString } from '@/utils'
import ClipboardJS from 'clipboard'
import message from '@/utils/message'
import { useI18n } from 'vue-i18n'
import DrawerEditNicknameFrom from '@/views/user/components/DrawerEditNicknameFrom/index.vue'

const { t } = useI18n()

const propsData = defineProps({
  userInfo: Object
})

const emits = defineEmits(['onRefresh'])

const drawerEditNicknameVisible = ref(false)

const onChangeDrawerEditNicknameVisible = (v) => {
  drawerEditNicknameVisible.value = v
}

const showEditNickname = () => {
  onChangeDrawerEditNicknameVisible(true)
}

watch(
  () => propsData.userInfo,
  () => {
    if (propsData.userInfo.nicknameFlag === '0') {
      showEditNickname()
    }
  }
)

const onEditNicknameSuc = () => {
  onChangeDrawerEditNicknameVisible(false)
  emits('onRefresh')
}

const onHandleCopy = (event, value) => {
  if (!value || value == '') {
    return
  }
  // 创建 ClipboardJS 实例并绑定到点击的元素
  const clipboard = new ClipboardJS(event.target, {
    text: () => value // 要复制的内容
  })

  // 监听复制成功事件
  clipboard.on('success', () => {
    message.success(t('copySuc'))
    // 复制成功后销毁 Clipboard 实例
    clipboard.destroy()
  })

  // 监听复制失败事件
  clipboard.on('error', () => {
    // alert('复制失败');
    // 复制失败后也销毁 Clipboard 实例
    clipboard.destroy()
  })

  // 手动触发点击事件，执行复制操作
  clipboard.onClick(event)
}

const extraIncomeRate = computed(() => {
  return Number((Number(propsData.userInfo?.extraIncomeRate ?? 0) * 100).toFixed(0))
})
const showExtraIncomeRate = computed(() => {
  const nodeId = propsData.userInfo?.nodeId
  return (
    extraIncomeRate.value > 0 && nodeId != '8' && nodeId != '9' && nodeId != '10' && nodeId != '11'
  )
})
const levelZeroFlag = computed(() => {
  return propsData.userInfo?.levelZeroFlag == '1'
})
</script>

<template>
  <div class="myProtfolio-container">
    <div class="myProtfolio-inner">
      <div class="myProtfolio-top">
        <div class="myProtfolio-top-l">
          <div class="icon-start-wrap">
            <img
              v-if="propsData.userInfo?.nodePic"
              class="icon-start"
              :src="propsData.userInfo?.nodePic"
            />
            <span
              v-if="showExtraIncomeRate"
              :class="{
                initial: levelZeroFlag
              }"
              >{{ extraIncomeRate }}</span
            >
          </div>
          <div class="txt">{{ $t('MyPortfolio') }}</div>
        </div>
        <div class="myProtfolio-top-r">
          <div class="wrap">
            <div class="left">
              <div class="name">
                <div class="txt1">{{ $t('Name') }}:</div>
                <div class="txt2">{{ propsData.userInfo?.nickname }}</div>
                <img
                  class="icon-edit cursor_p"
                  @click="showEditNickname"
                  src="@/assets/images/user/icon-edit.png"
                />
              </div>
              <div class="address">
                {{ $t('from') }}: {{ propsData.userInfo?.countryName }}-{{
                  propsData.userInfo?.area
                }}
              </div>
            </div>
            <div class="contact-wrap pc">
              <div class="item">
                <img src="@/assets/images/user/icon-telephone.png" />
                <div class="txt3_t">
                  +{{ propsData.userInfo?.interCode }} {{ propsData.userInfo?.mobile }}
                </div>
              </div>
              <div class="item">
                <img src="@/assets/images/user/icon-email.png" />
                <div class="txt3_t">{{ propsData.userInfo?.email }}</div>
              </div>
              <img
                class="icon-edit cursor_p"
                @click="showEditNickname"
                src="@/assets/images/user/icon-edit.png"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="myProtfolio-content">
        <div class="contact-wrap mobile">
          <div class="item">
            <img src="@/assets/images/user/icon-telephone.png" />
            <div class="txt3_t">
              +{{ propsData.userInfo?.interCode }} {{ propsData.userInfo?.mobile }}
            </div>
          </div>
          <div class="item">
            <img src="@/assets/images/user/icon-email.png" />
            <div class="txt3_t">{{ propsData.userInfo?.email }}</div>
          </div>
        </div>
        <div class="myProtfolio-content-item">
          <div class="txt1">
            <samp>{{ $t('Address') }}:</samp> {{ truncateString(propsData.userInfo?.address) }}
          </div>
          <img
            class="icon-copy cursor_p"
            src="@/assets/images/copy_white.png"
            @click="(e) => onHandleCopy(e, propsData.userInfo?.address)"
          />
        </div>
        <!-- productFlag 是否质押过 0:未质押 1:已质押 -->
        <div class="myProtfolio-content-item" v-if="propsData.userInfo?.productFlag == '1'">
          <div class="txt1">
            <samp>{{ $t('Invitation Code') }}:</samp> {{ propsData.userInfo?.inviteCode }}
          </div>
          <img
            class="icon-copy cursor_p"
            src="@/assets/images/copy_white.png"
            @click="(e) => onHandleCopy(e, propsData.userInfo?.inviteCode)"
          />
        </div>
      </div>
    </div>

    <DrawerEditNicknameFrom
      :visible="drawerEditNicknameVisible"
      :userInfo="propsData.userInfo ?? {}"
      @onChangeVisible="onChangeDrawerEditNicknameVisible"
      @onSuccess="onEditNicknameSuc"
    />
  </div>
</template>

<style scoped>
.myProtfolio-container {
  width: 100%;
  max-width: var(--content-width);
  margin: 0 auto;
  padding: 8px 15px 0;
  --star-size: 22px;
  --star-size-text: 12.5px;

  .myProtfolio-inner {
    width: 100%;
    height: 191px;
    background-image: url('@/assets/images/user/myProtfolio_bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .contact-wrap {
    flex: 1;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    margin: 20px 0 5px;
    &.mobile,
    &.mobile {
      display: flex;
    }
    &.pc,
    &.pc .icon-edit {
      display: none;
    }

    .item {
      display: flex;
      align-items: center;
      overflow: hidden;
      &:first-child {
        margin-left: 0;
      }

      img {
        height: 12px;
        margin-right: 6px;
      }
      .txt3_t {
        font-size: 12px;
        color: #ffffff;
        line-height: 17px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .icon-edit {
      margin-left: 33px;
    }
  }

  .myProtfolio-top {
    width: 100%;
    display: flex;
    .myProtfolio-top-l {
      display: flex;
      width: 42%;
      height: 41px;
      align-items: center;
      padding-left: 15px;
      .icon-start-wrap {
        position: relative;
        width: var(--star-size);
        height: var(--star-size);
        .icon-start {
          object-fit: cover;
          width: var(--star-size);
          height: var(--star-size);
        }
        span {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: var(--star-size-text);
          font-weight: 600;
          transform: scale(0.7);
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
          line-height: 1;
          &.initial {
            color: #666;
          }
        }
      }

      .icon-start {
        width: 17px;
      }
      .txt {
        font-size: 13px;
        margin-left: 4px;
      }
    }
    .myProtfolio-top-r {
      width: 58%;
      padding: 0 15px;
      display: flex;
      .txt1 {
        font-size: 13px;
        font-weight: 500;
        flex-shrink: 0;
      }
      .txt2 {
        flex: 1;
        margin: 0 6px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        font-size: 13px;
        font-weight: 500;
      }
      .icon-edit {
        width: 20px;
        flex-shrink: 0;
      }
      .wrap {
        width: 100%;
        height: 51px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left {
          width: 100%;
          overflow: hidden;
          .name {
            width: 100%;
            display: flex;
            align-items: center;
          }
          .address {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 17px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-top: 5px;
          }
          .icon-edit {
            display: inherit;
          }
        }
      }
    }
  }

  .myProtfolio-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 0 15px;
    .myProtfolio-content-item {
      width: 100%;
      height: 45px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      .txt1 {
        flex: 1;
        font-size: 13px;
        samp {
          color: rgba(255, 255, 255, 0.5);
        }
      }
      .icon-copy {
        flex-shrink: 0;
        width: 17px;
        margin-left: 10px;
      }
      &:last-child {
        border-bottom: none;
      }
    }
  }
}

@media (min-width: 1536px) {
  .myProtfolio-container {
    padding: 18px 50px 0;
  }
}
@media (min-width: 1024px) {
  .myProtfolio-container {
    padding: 18px 30px 0;
    --star-size: 24px;
    --star-size-text: 15px;

    .myProtfolio-inner {
      height: 123px !important;
      background-image: url('@/assets/images/user/web_myProtfolio_bg.png') !important;
    }

    .contact-wrap {
      flex: 1;
      width: 100%;
      align-items: center;
      justify-content: end;
      margin: 0 0 0 30px;
      &.mobile,
      &.mobile .icon-edit {
        display: none;
      }
      &.pc,
      &.pc .icon-edit {
        display: flex;
      }

      .item {
        margin-left: 59px;
        &:first-child {
          margin-left: 0;
        }

        img {
          height: 18px;
          margin-right: 3px;
        }
        .txt3_t {
          font-size: 14px;
          line-height: 20px;
        }
      }
      .icon-edit {
        margin-left: 33px;
        display: inherit;
      }
    }
    .myProtfolio-top {
      .myProtfolio-top-l {
        width: 16%;
        height: 48px;
        padding-left: 19px;
        .txt {
          font-size: 17px;
          margin-left: 6px;
        }
      }
      .myProtfolio-top-r {
        width: 84%;
        padding: 0 32px 0 16px;
        .txt1 {
          font-size: 17px;
        }
        .txt2 {
          font-size: 17px;
          flex: initial;
          margin: 0 8px;
        }
        .wrap {
          height: 61px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);

          .left {
            .address {
              font-size: 13px;
              line-height: 18px;
              margin-top: 0;
            }
            .icon-edit {
              display: none;
            }
          }
        }
      }
    }
    .myProtfolio-content {
      flex-direction: row;
      .myProtfolio-content-item {
        height: 61px;
        padding: 0 19px;
        border: none;
        .txt1 {
          flex: initial;
          font-size: 17px;
        }
        .icon-copy {
          width: 20px;
        }
      }
    }
  }
}
@media (max-width: 750px) {
}
</style>
