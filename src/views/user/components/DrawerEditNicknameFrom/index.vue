<script setup>
import { watch, ref, onMounted } from 'vue'
import { editUserProfile } from '@/api/user.js'
import message from '@/utils/message.js'
import { useI18n } from 'vue-i18n'
import { getCountryList } from '@/api/public.js'

const { t } = useI18n()

const propsData = defineProps({
  visible: Boolean,
  userInfo: Object
})

const emits = defineEmits(['onChangeVisible', 'onSuccess'])

const nickname = ref()
const email = ref()
const mobile = ref()
const region = ref()
const show = ref(false)
const loading = ref(false)
const maskClickFlag = ref(false)

const listVisible = ref(false)
const codeList = ref([])
const interCode = ref()
const interCodeIndex = ref(0)

const onChoseToggle = () => {
  listVisible.value = !listVisible.value
}
const choseCode = (item, index) => {
  interCode.value = item
  interCodeIndex.value = index
  listVisible.value = false
}

const onClose = () => {
  if (maskClickFlag.value) {
    emits('onChangeVisible', false)
  }
}

watch(
  () => propsData.visible,
  () => {
    if (propsData.visible) {
      // const uInfo = propsData.userInfo
      // nickname.value = (uInfo && uInfo.nickname) || null
      // email.value = (uInfo && uInfo.email) || null
      // mobile.value = (uInfo && uInfo.mobile) || null
      // region.value = (uInfo && uInfo.area) || null
      // let index = uInfo ? codeList.value.findIndex((item) => item.id == uInfo.countryCode) : -1
      // interCodeIndex.value = index > -1 ? index : 0
      // interCode.value = codeList.value[index]
      nickname.value = null
      email.value = null
      mobile.value = null
      region.value = null
      interCodeIndex.value = 0
      interCode.value = codeList.value[0]
    }
    listVisible.value = false
    show.value = propsData.visible
  }
)

watch(
  () => propsData.userInfo,
  () => {
    if (propsData.userInfo.nicknameFlag === '1') {
      maskClickFlag.value = true
    }
  }
)

const onSubmit = async () => {
  if (!nickname.value || nickname.value == '') {
    message.warning(t('Please enter your profile name'))
    return
  }

  try {
    await editUserProfile({
      nickname: nickname.value,
      email: email.value,
      mobile: mobile.value,
      countryId: mobile.value ? interCode.value.id : '',
      area: region.value
    })
    message.success(t('editSuc'))
    emits('onSuccess')
  } catch (e) {
    //
  }
}

onMounted(async () => {
  try {
    const res = await getCountryList()
    codeList.value = res
    interCode.value = res[0]
  } catch (e) {
    //
  }
})
</script>

<template>
  <Teleport to="body">
    <Transition name="edit-info">
      <div v-if="show" class="edit-info-modal-container">
        <div class="edit-info-mask" @click="onClose"></div>
        <div v-loading="loading" class="edit-info-box">
          <div class="edit-info-title">{{ $t('Please fill in details below') }}</div>
          <div class="send-container">
            <div class="from-item">
              <div class="label">{{ $t('Name') }}:</div>
              <div class="input-wrap">
                <el-input v-model="nickname" class="form-input" />
              </div>
            </div>
            <div class="from-item">
              <div class="label">{{ $t('E-mail') }}:</div>
              <div class="input-wrap">
                <el-input v-model="email" class="form-input" />
              </div>
            </div>
            <div class="from-item">
              <div class="label">{{ $t('Phone number') }}:</div>
              <div class="input-wrap">
                <div
                  @click="onChoseToggle"
                  class="code-chose"
                  :class="{
                    'chose-open': listVisible
                  }"
                >
                  <span>+ {{ codeList[interCodeIndex]?.interCode ?? '' }}</span>
                  <img src="@/assets/images/arrow_down.png" />
                  <i></i>
                </div>
                <el-input v-model="mobile" class="form-input" />
                <ul v-if="listVisible" class="code-list">
                  <li
                    v-for="(item, index) in codeList"
                    @click="choseCode(item, index)"
                    :key="item.id"
                    :class="{
                      active: interCodeIndex == index
                    }"
                  >
                    <div>{{ item.interName }}</div>
                    <div>{{ item.interCode }}</div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="from-item">
              <div class="label">{{ $t('Region') }}:</div>
              <div class="input-wrap">
                <el-input v-model="region" class="form-input" />
              </div>
            </div>
            <div class="btn-confirm cursor_p" @click="onSubmit">{{ $t('Done') }}</div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<style>
.edit-info-modal-container {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  --width: 359px;
  --radius: 18px;
  --padding: 28px 29px 26px;
  --title-bottom: 6px;
  --title-font-size: 18px;
  --btn-radius: 6px;
  --btn-font-size: 14px;

  --code-chose-font-size: 14px;
  --code-chose-icon-width: 20px;
  --code-chose-line-height: 16px;
  --code-list-radius: 7px;
  --code-list-top: 45px;
  --code-list-padding: 10px 12px;
  --code-list-item-radius: 6px;
  --code-list-item-top: 8px;
  --code-list-item-height: 32px;
  --code-list-item-font-size: 13px;
  --code-list-item-font-color: #fff;
  --code-list-item-stop-font-color: rgba(255, 255, 255, 0.3);

  .el-input__inner {
    color: #fff;
    font-size: 14px;
    line-height: 20px;
  }

  .send-container {
    height: 100%;
    margin-top: 27px;

    .from-item {
      text-align: left;
      margin-bottom: 14px;
      .label {
        font-size: 14px;
        color: #d2d6df;
        line-height: 22px;
        margin-bottom: 6px;
      }
      .input-wrap {
        width: 100%;
        height: 46px;
        background: rgba(71, 78, 92, 0.71);
        border-radius: 12px;
        border: 1px solid rgba(90, 97, 100, 0.71);
        display: flex;
        align-items: center;
        padding: 0 12px;
        position: relative;
      }
    }

    .btn-confirm {
      width: 100%;
      height: 46px;
      background: #13e6bc;
      border-radius: 6px;
      font-size: 14px;
      color: #ffffff;
      line-height: 46px;
      margin-top: 16px;
      text-align: center;
    }

    .code-chose {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      cursor: pointer;

      span {
        font-size: var(--code-chose-font-size);
        white-space: nowrap;
        color: #d2d6df;
      }
      img {
        width: var(--code-chose-icon-width);
        transition: 0.2s ease;
      }
      i {
        display: block;
        width: 1px;
        height: var(--code-chose-line-height);
        background-color: #979797;
        margin: 0 10px 0 3px;
      }

      &.chose-open img {
        transform: rotateZ(180deg);
      }
    }

    .code-list {
      position: absolute;
      top: var(--code-list-top);
      left: 0;
      z-index: 99;
      width: 100%;
      max-height: 200px;
      border-radius: var(--code-list-radius);
      overflow-x: hidden;
      overflow-y: auto;
      padding: var(--code-list-padding);
      background: #353c48;
      box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.75);

      li {
        border-radius: var(--code-list-item-radius);
        height: var(--code-list-item-height);
        display: flex;
        align-items: center;
        cursor: default;
        font-size: var(--code-list-item-font-size);
        color: var(--code-list-item-font-color);
        justify-content: space-between;
      }

      li:not(:first-child) {
        margin-top: var(--code-list-item-top);
      }

      li.active,
      li:hover {
        color: var(--primary-bg);
        cursor: pointer;
      }
    }
  }
  .edit-info-mask {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 9;
    background-color: rgba(0, 0, 0, 0.6);
  }
  .edit-info-box {
    width: var(--width);
    border-radius: var(--radius);
    background-color: var(--modal-bg);
    padding: var(--padding);
    position: relative;
    z-index: 10;
  }
  .edit-info-title {
    text-align: center;
    font-size: var(--title-font-size);
    font-weight: 600;
  }
}

.edit-info-enter-active,
.edit-info-leave-active {
  transition: opacity 0.5s ease;
}

.edit-info-enter-from,
.edit-info-leave-to {
  opacity: 0;
}
@media (max-width: 767px) {
  .edit-info-modal-container {
    --width: 323px;
    --radius: 15px;
    --padding: 26px 24px 30px;
    --title-bottom: 22px;
    --title-font-size: 14px;
    --btn-radius: 6px;
    --btn-font-size: 14px;

    --code-chose-font-size: 12px;
    --code-chose-icon-width: 13px;
    --code-chose-line-height: 13px;
    --code-list-radius: 6px;
    --code-list-top: 35px;
    --code-list-padding: 10px 12px;
    --code-list-item-radius: 6px;
    --code-list-item-top: 8px;
    --code-list-item-height: 22px;
    --code-list-item-font-size: 12px;

    .el-input__inner {
      font-size: 12px;
      line-height: 17px;
    }

    .send-container {
      margin-top: 21px;
      .from-item {
        margin-bottom: 18px;
        .label {
          font-size: 12px;
          line-height: 18px;
          margin-bottom: 6px;
        }
        .input-wrap {
          height: 37px;
          border-radius: 10px;
          padding: 0 10px;
        }
      }

      .btn-confirm {
        height: 37px;
        border-radius: 6px;
        font-size: 11px;
        line-height: 37px;
        margin-top: 16px;
      }
    }
  }
}
</style>
