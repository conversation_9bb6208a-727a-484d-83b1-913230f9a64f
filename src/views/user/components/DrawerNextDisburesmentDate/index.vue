<script setup>
import { watch, ref } from 'vue';
import { getPageProductOrderIcomeReleaseDetails } from '@/api/user.js'
import { formatDate } from '@/utils/index.js'

const propsData = defineProps({
  visible: <PERSON><PERSON>an,
  productOrderId: String,
  type: String,
});

const show = ref(false);
const list = ref([]);
const emits = defineEmits(["onChangeVisible"]);

const onClose = () => {
  emits("onChangeVisible", false);
};

watch(
  () => propsData.visible,
  () => {
    show.value = propsData.visible;
    if(show.value) {
      getInit();
    }
  }
);

const getInit = async () => {
  try {
    const res = await getPageProductOrderIcomeReleaseDetails({productOrderId: propsData.productOrderId, type: propsData.type});
    list.value = res;
  } catch (e) {
    //
  }
}

</script>

<template>
  <el-drawer v-model="show"
             direction="btt"
             class="drawer-wrapper disburesment-drawer"
             :destroy-on-close="true"
             :show-close="false"
             @close="onClose"
  >
    <template #header>
      <div class="drawer-header">
        <div class="title">{{$t('Next Disbursement Date')}}</div>
        <img class="icon-close" src="@/assets/images/close_w.png" @click="onClose">
      </div>
    </template>
    <template #default>
      <div class="disburesment-list">
        <div class="disburesment-item"
             :class="{'off': item.incomeDayFlag === '0' }"
             v-for="item in list"
             :key="item.id"
        >
          <div class="txt">{{formatDate(item.incomeDate, 'dd/MM/yyyy') }}</div>
          <div class="txt" v-if="item.incomeDayFlag === '0'">-</div>
          <div class="txt" v-else>{{item.transAmount}} FNXAI</div>
        </div>
      </div>
    </template>
  </el-drawer>
</template>

<style scoped>
.drawer-header{
  position: relative;
  padding: 5px 15px 0 15px;
  .title{
    width: 100%;
    font-size: 17px;
    color: #FFFFFF;
    line-height: 24px;
    text-align: center;
    border-bottom: 1px solid #474747;
    padding: 15px 0;
  }
  .icon-close{
    width: 22px;
    position: absolute;
    top: 19px;
    right: 12px;
    cursor: pointer;
  }
}
.disburesment-list{
  padding: 9px 15px;
  .disburesment-item{
    width: 100%;
    display: flex;
    height: 52px;
    background: #434A58;
    border-radius: 8px;
    margin-bottom: 9px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    .txt{
      font-size: 13px;
      color: #FFFFFF;
      line-height: 19px;
    }
    &.off .txt{
      color: #FF6767;
    }
  }
}
@media screen and (min-width: 1024px) {
  .disburesment-list{
    .disburesment-item{
      .txt{
        font-size: 14px;
      }
    }
  }
}
</style>
<style>
@media screen and (min-width: 1024px) {
  .disburesment-drawer.el-drawer{
    width: 375px !important;
  }
}
</style>
