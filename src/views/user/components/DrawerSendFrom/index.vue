<script setup>
import { watch, ref, onUnmounted } from 'vue'
import {
  encodeToHex,
  formatAmount,
  formatPrice,
  handleWeb3Error,
  isLogin,
  isPhone,
  truncateString
} from '@/utils'
import { getMyAccount, getProductOrderSumAmount, getUserInfo } from '@/api/user.js'
import {
  CHAINS,
  CHAINS_CONFIG,
  CHAINS_NAMES_MAP,
  ContractAbi,
  StakeContractAddress
} from '@/config/index.js'
import { getAccount, switchChain, writeContract, watchAccount } from '@wagmi/core'
import {
  getAccountWithdrawSign,
  getStakeWithdrawSign,
  stakeWithdrawCreate,
  accountWithdrawCreate, getProductAccountInfo
} from '@/api/stake.js'
import BigNumber from 'bignumber.js'
import message from '@/utils/message.js'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const chains = CHAINS_CONFIG.chains

const propsData = defineProps({
  visible: Boolean,
  type: String // 0:收益提币，1:奖励提币
})

const amount = ref(0)
const show = ref(false)
const balance = ref('0')
const accountInfo = ref()
const userInfo = ref()
const cChainId = ref()
const sendLoading = ref(false)

const emits = defineEmits(['onChangeVisible', 'onSuccess'])

const onClose = () => {
  emits('onChangeVisible', false)
}

const stockRatioDeduct = ref(0);
const stockRatio = () => {
  return formatPrice(stockRatioDeduct.value * amount.value )
}

const getInit = async () => {
  if (isLogin()) {
    const { chainId } = getAccount(CHAINS_CONFIG)
    const chain = chains.find((chain) => chain.id === chainId)
    if (chainId && !chain) {
      cChainId.value = CHAINS[0].id
    } else {
      cChainId.value = chainId
    }

    try {
      const info = await getUserInfo()
      userInfo.value = info

      if (propsData.type === '0') {
        const res = await getProductAccountInfo({ chainId: chainId })
        balance.value = res.withdrawIncome
        stockRatioDeduct.value = res.stockRatioDeduct;
      } else if (propsData.type === '1') {
        const res = await getMyAccount()
        balance.value = res.lockAmount
        accountInfo.value = res
      }
    } catch (e) {
      //
    }
  }
}

watch(
  () => propsData.visible,
  () => {
    show.value = propsData.visible
    if (propsData.visible) {
      getInit()
    } else {
      unwatchAccount()
    }
  }
)

const unwatchAccount = watchAccount(CHAINS_CONFIG, {
  async onChange(network, prevNetwork) {
    if (network.chainId != prevNetwork.chainId) {
      await getInit()
    }
  }
})

onUnmounted(() => {
  unwatchAccount()
})

const onMax = () => {
  amount.value = formatPrice(balance.value)
}

const onWithdraw0 = async () => {
  try {
    const { chainId } = getAccount(CHAINS_CONFIG)
    const chains = CHAINS_CONFIG.chains
    const chain = chains.find((chain) => chain.id === chainId)
    if (!chain || chain.unsupported) {
      sendLoading.value = false
      return
    }
    // loading.value = true;
    const res = await getStakeWithdrawSign({
      chainId: chainId,
      number: amount.value // 提币数量
    })
    const suffixParams = {
      type: propsData.type // 0:收益提币，1:奖励提币
    }
    const dataSuffix = encodeToHex(suffixParams)
    try {
      const hash = await writeContract(CHAINS_CONFIG, {
        address: StakeContractAddress[chainId],
        abi: ContractAbi[chainId],
        functionName: 'redeem',
        args: [res.amount, res.nonce, res.timestamp, res.message],
        dataSuffix: `0x${dataSuffix}`
      })
      stakeWithdrawCreate({
        chainId: chainId,
        transAmount: amount.value,
        txnHash: hash
      })
      sendLoading.value = false
      emits('onSuccess')
    } catch (error) {
      handleWeb3Error(error)
      sendLoading.value = false
      return
    }
    message.success(t('redeemSuc'))
  } catch (error) {
    sendLoading.value = false
  }
}

const onWithdraw1 = async () => {
  try {
    const { chainId } = getAccount(CHAINS_CONFIG)
    const chains = CHAINS_CONFIG.chains
    const chain = chains.find((chain) => chain.id === chainId)
    if (!userInfo.value.withdrawChainId) {
      return
    }
    const withdrawChainId = Number(userInfo.value.withdrawChainId)
    if (!chain || chain.unsupported || chain.id != withdrawChainId) {
      await switchChain(CHAINS_CONFIG, {
        chainId: withdrawChainId
      })
    }
    const res = await getAccountWithdrawSign({
      chainId: withdrawChainId,
      number: amount.value // 提币数量
    })
    const suffixParams = {
      type: propsData.type // 0:收益提币，1:奖励提币
    }
    const dataSuffix = encodeToHex(suffixParams)
    try {
      const hash = await writeContract(CHAINS_CONFIG, {
        address: StakeContractAddress[withdrawChainId],
        abi: ContractAbi[withdrawChainId],
        functionName: 'redeem',
        args: [res.amount, res.nonce, res.timestamp, res.message],
        dataSuffix: `0x${dataSuffix}`
      })
      accountWithdrawCreate({
        chainId: withdrawChainId,
        amount: amount.value,
        txnHash: hash
      })

      sendLoading.value = false
      emits('onSuccess')
    } catch (error) {
      handleWeb3Error(error)
      sendLoading.value = false
      return
    }
    message.success(t('redeemSuc'))
  } catch (error) {
    sendLoading.value = false
  }
}

const onSubmit = async () => {
  if (sendLoading.value) {
    return
  }
  if (balance.value <= 0 || amount.value > balance.value) {
    message.warning(t('Available Balance is not enough'))
    return
  }
  if (amount.value <= 0) {
    message.warning(t('Please enter transfer amount'))
    return
  }
  sendLoading.value = true
  if (propsData.type === '0') {
    onWithdraw0()
  }
  if (propsData.type === '1') {
    onWithdraw1()
  }
}

const amountInput = (v) => {
  let inputValue = formatAmount(v, 4)

  const a = new BigNumber(inputValue)
  const b = new BigNumber(balance.value.toString())

  // 检查输入是否有效
  if (inputValue === '' || isNaN(a.toNumber())) {
    amount.value = inputValue // 允许清空输入
    return
  }

  if (a.isGreaterThan(b)) {
    amount.value = balance.value.toString()
  } else {
    amount.value = inputValue
  }
}
</script>

<template>
  <el-drawer
    v-model="show"
    :direction="isPhone() ? 'rtl' : 'btt'"
    class="drawer-wrapper send-drawer"
    :destroy-on-close="true"
    :show-close="false"
    @close="onClose"
  >
    <template #header>
      <div class="drawer-header">
        <img class="icon-left" src="@/assets/images/user/icon_left_circular.png" @click="onClose" />
        <div class="title">{{ $t('Send') }}</div>
        <img class="icon-close" src="@/assets/images/close_w.png" @click="onClose" />
      </div>
    </template>
    <template #default>
      <div class="send-container">
        <div class="from-item">
          <div class="label">{{ $t('Your Wallet Address') }}</div>
          <div class="input-wrap">
            <div class="value">
              {{ isPhone() ? truncateString(userInfo?.address, 26, 8, 34) : userInfo?.address }}
            </div>
          </div>
        </div>
        <div class="from-item">
          <div class="label">{{ $t('Network') }}</div>
          <div class="input-wrap">
            <img
              v-if="cChainId == CHAINS[0].id"
              class="input-icon"
              src="@/assets/images/eth_s.png"
            />
            <img
              v-if="cChainId == CHAINS[1].id"
              class="input-icon"
              src="@/assets/images/arbitrum_s.png"
            />
            <img
              v-if="cChainId == CHAINS[2].id"
              class="input-icon"
              src="@/assets/images/bnb_s.png"
            />
            <div class="value">{{ CHAINS_NAMES_MAP[cChainId] }}</div>
          </div>
          <div class="describe">{{ $t('Amount will ... blockchain network') }}</div>
        </div>
        <div class="from-item">
          <div class="label">{{ $t('Transfer') }}</div>
          <div class="input-wrap textarea">
            <div class="input">
              <el-input v-model="amount" class="form-input" @input="amountInput" />
              <div class="btn-max cursor_p" @click="onMax">{{ $t('Max') }}</div>
            </div>
            <!--  // 0:收益提币，1:奖励提币  -->
            <div class="balance_wrap" v-if="type === '0'">
              <div class="balance">
                <div class="name">{{ $t('Available Balance') }}{{ isPhone() ? '' : ':' }}</div>
                <div class="amount">
                  <div class="txt">{{ formatPrice(balance) }}</div>
                  <img class="amount-icon" src="@/assets/images/coin_icon.png" />
                </div>
              </div>
              <div class="balance">
                <div class="name">{{ $t('Deduction') }}{{ isPhone() ? '' : ':' }}</div>
                <div class="amount">
                  <div class="txt deduction">{{amount > 0 ? '-' : ''}}{{ stockRatio() }} {{ $t('Shares') }}</div>
                </div>
              </div>
            </div>
           <div class="balance_wrap" v-else>
            <div class="balance">
              <div class="name">{{ $t('Withdraw Amount') }}{{ isPhone() ? '' : ':' }}</div>
              <div class="amount">
                <div class="txt">{{ formatPrice(balance) }}</div>
                <img class="amount-icon" src="@/assets/images/coin_icon.png" />
              </div>
            </div>
          </div>
<!--            <div class="balance_wrap" v-else>
              <div class="balance">
                <div class="name">{{ $t('Current balance1') }}{{ isPhone() ? '' : ':' }}</div>
                <div class="amount">
                  <div class="txt">{{ formatPrice(balance) }}</div>
                  <img class="amount-icon" src="@/assets/images/coin_icon.png" />
                </div>
              </div>
              <div class="balance" v-if="accountInfo?.toSettleAmount > 0">
                <div class="name">{{ $t('Pending Balance') }}{{ isPhone() ? '' : ':' }}</div>
                <div class="amount">
                  <div class="txt">{{ formatPrice(accountInfo?.toSettleAmount) }}</div>
                  <img class="amount-icon" src="@/assets/images/coin_icon.png" />
                </div>
              </div>
            </div>-->
          </div>
        </div>
<!--
        <div class="balance-remark" v-if="type === '1' && accountInfo?.toSettleAmount > 0">
          {{ $t('WithdrawRemark') }}
        </div>-->
        <div class="btn-confirm cursor_p" @click="onSubmit" v-loading="sendLoading">
          {{ $t('Confirm') }}
        </div>
      </div>
    </template>
  </el-drawer>
</template>

<style scoped>
.drawer-header {
  position: relative;
  padding: 5px 15px 0 15px;
  .title {
    width: 100%;
    font-size: 17px;
    color: #ffffff;
    line-height: 24px;
    text-align: center;
    padding: 15px 0;
  }
  .icon-close {
    width: 22px;
    position: absolute;
    top: 19px;
    right: 12px;
    display: none;
  }
  .icon-left {
    width: 35px;
    position: absolute;
    top: 12px;
    left: 15px;
  }
}
.send-container {
  padding: 9px 15px;
  height: 100%;
  overflow-y: auto;
  text-align: center;

  .from-item {
    text-align: left;
    margin-bottom: 18px;
    .label {
      font-size: 15px;
      color: #ffffff;
      line-height: 21px;
      margin-bottom: 10px;
    }
    .input-wrap {
      width: 100%;
      height: 61px;
      background: rgba(50, 56, 68, 0.71);
      border-radius: 17px;
      border: 1px solid #5a6164;
      display: flex;
      align-items: center;
      padding: 0 15px;
      &.textarea {
        height: auto;
        flex-direction: column;
        padding-bottom: 15px;
        .input {
          border-bottom: 1px solid #3e494d;
          width: 100%;
          display: flex;
          align-items: center;
          padding: 25px 0 19px;
        }
        .btn-max {
          height: 27px;
          background: #13e6bc;
          border-radius: 4px;
          font-size: 13px;
          color: #ffffff;
          line-height: 27px;
          padding: 0 10px;
          flex-shrink: 0;
          margin-left: 10px;
        }
      }
      .value {
        font-size: 15px;
        color: rgba(255, 255, 255, 0.5);
        line-height: 21px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .input-icon {
        height: 30px;
        margin-right: 15px;
      }

      .balance {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 15px;
        .name {
          font-size: 12px;
          color: #ffffff;
          line-height: 17px;
        }
        .amount {
          display: flex;
          align-items: center;
          .txt {
            font-size: 11px;
            color: #ffffff;
            line-height: 15px;
            &.deduction{
              color: #13e6bc;
            }
          }
        }
        .amount-icon {
          height: 15px;
          margin-left: 5px;
        }
      }
      .balance_wrap {
        width: 100%;
        display: flex;
        flex-direction: column;
      }
    }
    .describe {
      font-size: 12px;
      color: #c4c4c4;
      line-height: 16px;
      margin-top: 9px;
    }
  }
  .balance-remark {
    width: 100%;
    font-size: 11px;
    color: #c4c4c4;
    line-height: 15px;
    text-align: left;
  }
  .btn-confirm {
    width: 100%;
    height: 43px;
    background: #13e6bc;
    border-radius: 6px;
    font-size: 14px;
    color: #ffffff;
    line-height: 43px;
    margin-top: 23px;
  }
}
@media screen and (min-width: 1024px) {
  .drawer-header {
    .icon-close {
      display: block;
    }
    .icon-left {
      display: none;
    }
  }
  .send-container {
    padding: 0 28px;
    .from-item .input-wrap .balance_wrap {
      flex-direction: row;
      justify-content: space-between;
      .balance {
        width: auto;
        justify-content: start;
      }
    }
  }
}
</style>
<style>
.send-drawer.el-drawer {
  width: 100% !important;
  height: 100vh !important;
  max-height: initial !important;
  min-height: initial !important;
  border-radius: 0;
  background-image: url('@/assets/images/user/user_bg.png');
  background-repeat: no-repeat;
  background-position: top center;
  background-size: 100% auto;
  background-color: #020e24;
}
@media screen and (min-width: 1024px) {
  .send-drawer.el-drawer {
    width: 530px !important;
    max-height: 590px !important;
    min-height: 300px !important;
    border-radius: 15px;
    background-color: #171c22;
    background-image: none;
  }
}
</style>
