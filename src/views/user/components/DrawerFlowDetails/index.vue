<script setup>
import { watch, ref } from 'vue';
import { formatDate, formatPrice, isPhone, truncateString } from '@/utils'
import { getProductOrderIncomeDetail } from '@/api/user.js'
import ClipboardJS from 'clipboard';
import message from '@/utils/message'
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// {0:收益,1:提币,3:定期质押收益 4: 复投 5：划转}
const typeList = {
  0: t('dailyInterestAmount'),
  1: t('WithdrawDict'),
  3: t('stakingReward'),
  4: t('Restake'),
  5: t('transfer'),
}

const statusList = {
  '0': t('Pending processing'),
  '1': t('Successful'),
}

const propsData = defineProps({
  visible: Boolean,
  id: String,
});

const show = ref(false);
const emits = defineEmits(["onChangeVisible"]);
const detailInfo = ref();

const onClose = () => {
  emits("onChangeVisible", false);
};

watch(
  () => propsData.visible,
  () => {
    show.value = propsData.visible;
    if(propsData.visible) {
      getInit()
    }
  }
);

const getInit = async () => {
  if(!propsData.id) {
    return;
  }
  try {
    const res = await getProductOrderIncomeDetail(propsData.id);
    detailInfo.value = res;
  } catch (e) {
    //
  }

}

const onHandleCopy = (event, value) => {
  if(!value || value == "") {
    return
  }
// 创建 ClipboardJS 实例并绑定到点击的元素
  const clipboard = new ClipboardJS(event.target, {
    text: () => value,  // 要复制的内容
  });

  // 监听复制成功事件
  clipboard.on('success', () => {
    message.success(t('copySuc'))
    // 复制成功后销毁 Clipboard 实例
    clipboard.destroy();
  });

  // 监听复制失败事件
  clipboard.on('error', () => {
    // alert('复制失败');
    // 复制失败后也销毁 Clipboard 实例
    clipboard.destroy();
  });

  // 手动触发点击事件，执行复制操作
  clipboard.onClick(event);
}
</script>

<template>
  <el-drawer v-model="show"
             :direction="isPhone() ? 'rtl' : 'btt'"
             class="drawer-wrapper flowDetails-drawer"
             :destroy-on-close="true"
             :show-close="false"
             @close="onClose"
  >
    <template #header>
      <div class="drawer-header">
        <img class="icon-left" src="@/assets/images/user/icon_left_circular.png" @click="onClose">
        <div class="title">{{$t('Details')}}</div>
        <img class="icon-close" src="@/assets/images/close_w.png" @click="onClose">
      </div>
    </template>
    <template #default>
      <div class="flowDetails-container">
        <img v-if="detailInfo?.type === '0' || detailInfo?.type === '3'" class="top-icon" src="@/assets/images/user/icon-in.png">
        <img v-if="detailInfo?.type === '1' || detailInfo?.type === '4'" class="top-icon" src="@/assets/images/user/icon-out.png">
        <img
          v-if="detailInfo?.type === '5'"
          class="icon icon-transfer"
          src="@/assets/images/user/round_transfer.png"
        />
        <div class="top-txt1">{{formatPrice(detailInfo?.transAmount)}} FNXAI</div>
        <div class="top-txt2">{{typeList[detailInfo?.type]}}</div>
        <div class="flowDetails-list" v-if="detailInfo?.type == '0' || detailInfo?.type == '3'">
          <div class="flowDetails-item">
            <div class="label">{{  $t('Received On') }}</div>
            <div class="value">
              <div class="txt">{{formatDate(detailInfo?.incomeDate, 'yyyy年MM月dd日')}}</div>
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Network') }}</div>
            <div class="value">
              <div class="txt">{{detailInfo?.chainName}}</div>
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Wallet') }}</div>
            <div class="value">
              <div class="txt">{{truncateString(detailInfo?.address ?? '')}}</div>
              <img
                class="value-icon cursor_p"
                src="@/assets/images/user/icon_copy_green.png"
                @click="(e) => onHandleCopy(e, detailInfo?.address)"
              >
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Result') }}</div>
            <div class="value">
              <div class="txt">{{statusList[detailInfo?.status]}}</div>
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Daily Payout') }}</div>
            <div class="value">
              <div class="txt">{{detailInfo?.dailyInterestRate}}%</div>
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Staked Time') }}</div>
            <div class="value">
              <div class="txt">{{formatDate(detailInfo?.buyTime, 'yyyy年MM月dd日 hh:mm:ss')}}</div>
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Staked Amount') }}</div>
            <div class="value">
              <div class="txt">{{detailInfo?.buyAmount}} FNXAI</div>
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Remark') }}</div>
            <div class="value">
              <div class="txt">{{detailInfo?.remark}}</div>
            </div>
          </div>
        </div>
        <div class="flowDetails-list" v-else-if="detailInfo?.type === '4'">
          <div class="flowDetails-item">
            <div class="label">{{ $t('Restake Time') }}</div>
            <div class="value">
              <div class="txt">
                {{ formatDate(detailInfo?.createDatetime, 'yyyy年MM月dd日 hh:mm:ss') }}
              </div>
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Network') }}</div>
            <div class="value">
              <div class="txt">{{ detailInfo?.chainName }}</div>
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Wallet') }}</div>
            <div class="value">
              <div class="txt">{{ truncateString(detailInfo?.address) }}</div>
              <img
                class="value-icon cursor_p"
                src="@/assets/images/user/icon_copy_green.png"
                @click="(e) => onHandleCopy(e, detailInfo?.address)"
              />
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Result') }}</div>
            <div class="value">
              <div class="txt">{{ statusList[detailInfo?.status] }}</div>
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Restake Amount') }}</div>
            <div class="value">
              <div class="txt">{{ detailInfo?.transAmount }} FNXAI</div>
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Remark') }}</div>
            <div class="value">
              <div class="txt">{{ detailInfo?.remark }}</div>
            </div>
          </div>
        </div>
        <div class="flowDetails-list" v-else-if="detailInfo?.type === '5'">
          <div class="flowDetails-item">
            <div class="label">{{ $t('Transfer Time') }}</div>
            <div class="value">
              <div class="txt">
                {{ formatDate(detailInfo?.createDatetime, 'yyyy年MM月dd日 hh:mm:ss') }}
              </div>
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Wallet') }}</div>
            <div class="value">
              <div class="txt">{{ truncateString(detailInfo?.address) }}</div>
              <img
                class="value-icon cursor_p"
                src="@/assets/images/user/icon_copy_green.png"
                @click="(e) => onHandleCopy(e, detailInfo?.address)"
              />
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Result') }}</div>
            <div class="value">
              <div class="txt">{{ statusList[detailInfo?.status] }}</div>
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Remark') }}</div>
            <div class="value">
              <div class="txt">{{ detailInfo?.remark }}</div>
            </div>
          </div>
        </div>
        <div class="flowDetails-list" v-else>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Withdraw On') }}</div>
            <div class="value">
              <div class="txt">{{formatDate(detailInfo?.createDatetime, 'yyyy年MM月dd日 hh:mm:ss')}}</div>
            </div>
          </div>
          <div v-if="detailInfo?.txnHash" class="flowDetails-item">
            <div class="label">{{ $t('Hash') }}</div>
            <div class="value">
              <div class="txt">{{truncateString(detailInfo?.txnHash)}}</div>
              <img
                class="value-icon cursor_p"
                src="@/assets/images/user/icon_copy_green.png"
                @click="(e) => onHandleCopy(e, detailInfo?.txnHash)"
              />
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Network') }}</div>
            <div class="value">
              <div class="txt">{{detailInfo?.chainName}}</div>
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Wallet') }}</div>
            <div class="value">
              <div class="txt">{{truncateString(detailInfo?.address)}}</div>
              <img
                class="value-icon cursor_p"
                src="@/assets/images/user/icon_copy_green.png"
                @click="(e) => onHandleCopy(e, detailInfo?.wallet)"
              />
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Result') }}</div>
            <div class="value">
              <div class="txt">{{statusList[detailInfo?.status]}}</div>
            </div>
          </div>
          <div class="flowDetails-item">
            <div class="label">{{ $t('Remark') }}</div>
            <div class="value">
              <div class="txt">{{detailInfo?.remark}}</div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </el-drawer>
</template>

<style scoped>
.drawer-header{
  position: relative;
  padding: 5px 15px 0 15px;
  .title{
    width: 100%;
    font-size: 17px;
    color: #FFFFFF;
    line-height: 24px;
    text-align: center;
    padding: 15px 0;
  }
  .icon-close{
    width: 22px;
    position: absolute;
    top: 19px;
    right: 12px;
    display: none;
  }
  .icon-left{
    width: 35px;
    position: absolute;
    top: 12px;
    left: 15px;
  }
}
.flowDetails-container{
  padding: 9px 15px;
  height: 100%;
  overflow-y: auto;
  text-align: center;

  .top-icon{
    height: 48px;
    margin-top: 15px;
  }
  .top-txt1{
    font-size: 23px;
    color: #FFFFFF;
    line-height: 33px;
    margin-top: 21px;
  }
  .top-txt2{
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 17px;
    margin-bottom: 5px;
  }
  .flowDetails-list{
    width: 100%;
    margin: 24px 0;
    background: #323844;
    border-radius: 17px;
    border: 1px solid #5A6164;
    padding: 0 17px;
    .flowDetails-item{
      width: 100%;
      height: 55px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #565656;
      .label{
        font-size: 13px;
        color: rgba(255, 255, 255, 0.7);
        line-height: 17px;
        flex-shrink: 0;
        margin-right: 10px;
      }
      .value{
        flex: 1;
        display: flex;
        align-items: center;
        overflow: hidden;
        .txt{
          flex: 1;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          font-size: 13px;
          color: #FFFFFF;
          line-height: 17px;
          text-align: right;
        }
        .value-icon{
          height: 22px;
          flex-shrink: 0;
        }
      }
    }
  }
}
@media screen and (min-width: 1024px) {
  .drawer-header{
    .title{
      display: none;
    }
    .icon-close{
      display: block;
    }
    .icon-left{
      display: none;
    }
  }
  .flowDetails-container{
    .top-icon{
      height: 40px;
    }
    .top-txt1{
      font-size: 20px;
      line-height: 28px;
      margin-top: 11px;
    }
    .top-txt2{
      font-size: 13px;
      line-height: 18px;
      margin-top: 2px;
    }
    .flowDetails-list{
      border: none;
      background: none;
      margin-top: 18px;
      padding: 0 15px;
      .flowDetails-item{
        .flowDetails-item{
          .label{
            font-size: 16px;
          }
          .value{
            .txt{
              font-size: 16px;
            }
          }
        }
      }
    }
  }
}
</style>
<style>
.flowDetails-drawer.el-drawer{
  width: 100% !important;
  height: 100vh !important;
  max-height: initial !important;
  min-height: initial !important;
  border-radius: 0;
  background-image: url("@/assets/images/user/user_bg.png");
  background-repeat: no-repeat;
  background-position: top center;
  background-size:100%  auto;
  background-color: #020E24;
}
@media screen and (min-width: 1024px) {
  .flowDetails-drawer.el-drawer{
    width: 470px !important;
    max-height: 620px !important;
    min-height: 300px !important;
    border-radius: 15px;
    background-color: #2E343F;
  }
}
</style>
