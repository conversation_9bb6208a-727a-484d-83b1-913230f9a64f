<script setup>
import { watch, ref } from 'vue'
import { formatDate, formatPrice, isPhone } from '@/utils'
import { getStockJourDetail } from '@/api/user.js'

const propsData = defineProps({
  visible: Boolean,
  id: String
})

const show = ref(false)
const emits = defineEmits(['onChangeVisible'])
const detailInfo = ref()

const onClose = () => {
  emits('onChangeVisible', false)
}

watch(
  () => propsData.visible,
  () => {
    show.value = propsData.visible
    if (propsData.visible) {
      getInit();
    }
  }
)


const getInit = async () => {
  if (!propsData.id) {
    return
  }
  try {
    const res = await getStockJourDetail(propsData.id)
    detailInfo.value = res
  } catch (e) {
    //
  }
}

</script>

<template>
  <el-drawer
    v-model="show"
    :direction="isPhone() ? 'rtl' : 'btt'"
    class="drawer-wrapper sharesJourDetails-drawer"
    :destroy-on-close="true"
    :show-close="false"
    @close="onClose"
  >
    <template #header>
      <div class="drawer-header">
        <img class="icon-left" src="@/assets/images/user/icon_left_circular.png" @click="onClose" />
        <div class="title">{{ $t('Details') }}</div>
        <img class="icon-close" src="@/assets/images/close_w.png" @click="onClose" />
      </div>
    </template>
    <template #default>
      <div class="sharesJourDetails-container">
        <img
          class="top-icon"
          v-if="detailInfo?.transAmount < 0"
          src="@/assets/images/user/icon-out.png"
        />
        <img class="top-icon" v-else src="@/assets/images/user/icon-in.png" />
        <div class="top-txt1">{{ formatPrice(detailInfo?.transAmount) }}</div>
        <div class="top-txt2">{{ detailInfo?.bizNote }}</div>
        <!--   type  {0:每日奖励,1:推荐奖励, 2:提币, 3股东分红, 5:卓越奖励, 6:复投, 8:转账}     -->
        <div class="sharesJourDetails-list">
          <div class="sharesJourDetails-item">
            <div class="label">{{ $t('Date') }}</div>
            <div class="value">
              <div class="txt">
                {{ formatDate(detailInfo?.createTime, 'yyyy年MM月dd日 hh:mm:ss') }}
              </div>
            </div>
          </div>
          <template v-if="detailInfo?.transAmount < 0">
            <div class="sharesJourDetails-item" >
              <div class="label">{{ $t('Withdrawal Amount') }}</div>
              <div class="value">
                <div class="txt">{{ detailInfo?.transAmount }}</div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="sharesJourDetails-item" >
              <div class="label">{{ $t('Distribution Amount') }}</div>
              <div class="value">
                <div class="txt">{{ detailInfo?.transAmount }}</div>
              </div>
            </div>
            <div class="sharesJourDetails-item">
              <div class="label">{{ $t('Distribution Date') }}</div>
              <div class="value">
                <div class="txt">
                  {{ formatDate(detailInfo?.createTime, 'yyyy年MM月dd日 hh:mm:ss') }}
                </div>
              </div>
            </div>
          </template>
          <div class="sharesJourDetails-item">
            <div class="label">{{ $t('FNXAI : Shares Ratio') }}</div>
            <div class="value">
              <div class="txt">1:{{ detailInfo?.rate }}</div>
            </div>
          </div>
          <div class="sharesJourDetails-item remark">
            <div class="label">{{ $t('Remark') }}</div>
            <div class="value">
              <div class="txt">{{ $t('S/N') }}&nbsp;{{ detailInfo?.refBizNo }}; {{ formatDate(detailInfo?.createTime, 'yyyy年MM月dd日 hh:mm:ss') }}</div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </el-drawer>
</template>

<style scoped>
.drawer-header {
  position: relative;
  padding: 5px 15px 0 15px;
  .title {
    width: 100%;
    font-size: 17px;
    color: #ffffff;
    line-height: 24px;
    text-align: center;
    padding: 15px 0;
  }
  .icon-close {
    width: 22px;
    position: absolute;
    top: 19px;
    right: 12px;
    display: none;
  }
  .icon-left {
    width: 35px;
    position: absolute;
    top: 12px;
    left: 15px;
  }
}
.sharesJourDetails-container {
  padding: 9px 15px;
  height: 100%;
  overflow-y: auto;
  text-align: center;

  .top-icon {
    height: 48px;
    margin-top: 15px;
  }
  .top-txt1 {
    font-size: 23px;
    color: #ffffff;
    line-height: 33px;
    margin-top: 21px;
  }
  .top-txt2 {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 17px;
    margin-bottom: 5px;
  }
  .sharesJourDetails-list {
    width: 100%;
    margin: 24px 0;
    background: #323844;
    border-radius: 17px;
    border: 1px solid #5a6164;
    padding: 0 17px;
    .sharesJourDetails-item {
      width: 100%;
      height: 55px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #565656;
      .label {
        font-size: 13px;
        color: rgba(255, 255, 255, 0.7);
        line-height: 17px;
        flex-shrink: 0;
        margin-right: 10px;
      }
      .value {
        flex: 1;
        display: flex;
        align-items: center;
        overflow: hidden;
        .txt {
          flex: 1;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          font-size: 13px;
          color: #ffffff;
          line-height: 17px;
          text-align: right;
        }
        .value-icon {
          height: 22px;
          flex-shrink: 0;
        }
      }
    }
  }
}
@media screen and (min-width: 1024px) {
  .drawer-header {
    .title {
      display: none;
    }
    .icon-close {
      display: block;
    }
    .icon-left {
      display: none;
    }
  }
  .sharesJourDetails-container {
    .top-icon {
      height: 40px;
    }
    .top-txt1 {
      font-size: 20px;
      line-height: 28px;
      margin-top: 11px;
    }
    .top-txt2 {
      font-size: 13px;
      line-height: 18px;
      margin-top: 2px;
    }
    .sharesJourDetails-list {
      border: none;
      background: none;
      margin-top: 18px;
      padding: 0 15px;
      .sharesJourDetails-item {
        .label {
          font-size: 16px;
        }
        .value {
          .txt {
            font-size: 16px;
          }
        }
        &.remark {
          height: auto;
          padding: 15px 0;
          align-items: start;
          overflow: initial;
          .value {
            .txt {
              white-space: initial;
              text-overflow: initial;
              overflow: initial;
            }
          }
        }
      }
    }
  }
}
</style>
<style>
.sharesJourDetails-drawer.el-drawer {
  width: 100% !important;
  height: 100vh !important;
  max-height: initial !important;
  min-height: initial !important;
  border-radius: 0;
  background-image: url('@/assets/images/user/user_bg.png');
  background-repeat: no-repeat;
  background-position: top center;
  background-size: 100% auto;
  background-color: #020e24;
}
@media screen and (min-width: 1024px) {
  .sharesJourDetails-drawer.el-drawer {
    width: 470px !important;
    max-height: 620px !important;
    min-height: 300px !important;
    border-radius: 15px;
    background-color: #2e343f;
  }
}
</style>
