<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import MyProtfolio from './components/MyProtfolio/index.vue'
import { isLogin } from '@/utils'
import { useUserStore } from '@/stores/user'

const route = useRoute()
const router = useRouter()

const activeName = ref('myStake')
const userInfo = ref()
const userStore = useUserStore()

const handleChange = (tabName) => {
  // if (activeName.value != tab.paneName) {
  activeName.value = tabName

  router.push({
    path: `/user/${tabName}`,
    query: { ...route.query, active: tabName }
  })
  // }
}

const getInit = async () => {
  if (!isLogin()) return

  try {
    const res = await userStore.getUserMsg()
    userInfo.value = res

    const activeParam = route.query.active

    if (res.referUserCount > 0) {
      activeName.value = activeParam ?? 'myStake'
    } else {
      activeName.value = 'myStake'
    }
  } catch (e) {
    //
  }
}

// 页面加载时获取路由参数
onMounted(() => {
  getInit()
})
</script>

<template>
  <div class="user-container">
    <MyProtfolio :userInfo="userInfo" @onRefresh="getInit" />
    <div class="user-menu-container">
      <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleChange" style="flex: 1">
        <el-tab-pane :label="$t('My Stake')" name="myStake">
          <RouterView v-if="activeName === 'myStake'" />
        </el-tab-pane>
        <el-tab-pane
          :label="$t('My List')"
          name="myList"
          :lazy="true"
          v-if="userInfo?.referUserCount > 0"
        >
          <RouterView v-if="activeName === 'myList'" />
        </el-tab-pane>
        <el-tab-pane
          :label="$t('Daily Bonus')"
          name="dailyBonus"
          :lazy="true"
          v-if="userInfo?.referUserCount > 0"
        >
          <RouterView v-if="activeName === 'dailyBonus'" />
        </el-tab-pane>
        <el-tab-pane
          :label="$t('Bonus Wallet')"
          name="bonusWallet"
          :lazy="true"
          v-if="userInfo?.referUserCount > 0"
        >
          <RouterView v-if="activeName === 'bonusWallet'" />
        </el-tab-pane>
        <el-tab-pane
          :label="$t('Shares Wallet')"
          name="sharesWallet"
          :lazy="true"
          v-if="userInfo?.referUserCount > 0"
        >
          <RouterView v-if="activeName === 'sharesWallet'" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<style>
.user-container {
  width: 100%;
  max-width: var(--content-width);
  margin: 0 auto;
  padding-top: calc(var(--header-height) + 10px);
  min-height: calc(100vh - var(--footer-height));
  background-image: url('@/assets/images/user/user_bg.png');
  background-repeat: no-repeat;
  background-position: top center;
  background-size: 100% auto;
  display: flex;
  flex-direction: column;

  .user-menu-container {
    width: 100%;
    margin: 18px auto;
    padding-left: 15px;
    flex: 1;
    flex-direction: column;
    display: flex;
    .el-tabs__content {
      flex-direction: column;
      display: flex;
    }
    .el-tab-pane {
      flex: 1;
      flex-direction: column;
      display: flex;
    }
  }

  @media (min-width: 1536px) {
  }
  @media (min-width: 1024px) {
    background: none !important;
    .user-menu-container {
      max-width: var(--content-width);
      padding: 0 30px;
      margin-top: 28px !important;
    }
  }
  @media (max-width: 767px) {
  }
}
</style>
