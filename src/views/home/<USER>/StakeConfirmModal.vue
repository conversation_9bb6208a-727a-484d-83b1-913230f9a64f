<script setup>
import { productOrde<PERSON><PERSON><PERSON><PERSON>, productOrderReinvest, stakeCreate } from '@/api/stake'
import { CHAINS_CONFIG, ContractAbi, StakeContractAddress } from '@/config'
import { useUserStore } from '@/stores/user'
import { encodeToHex, handleWeb3Error, toBigNumber } from '@/utils'
import message from '@/utils/message'
import { getAccount, writeContract } from '@wagmi/core'
import dayjs from 'dayjs'
import { debounce } from 'lodash'
import { watch, ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  visible: Boolean,
  info: Object,
  amount: String,
  inviteCode: String,
  noteTip: String,
  type: {
    type: String,
    validator: (value) => ['stake', 'reinvest'].includes(value),
    default: 'stake'
  },
  accountType: {
    type: String,
    // 0：收益 1：奖励, 复投时候用
    validator: (value) => ['0', '1'].includes(value),
    default: '0'
  }
})

const emit = defineEmits(['cancel', 'success'])

const { locale, t } = useI18n()

const onCancel = () => {
  emit('cancel')
}

const loading = ref(false)
const userStore = useUserStore()
const showInviteCode = computed(() => {
  return userStore.user && userStore.user.productFlag == '0'
})

const callContract = async () => {
  try {
    const { chainId } = getAccount(CHAINS_CONFIG)

    const chains = CHAINS_CONFIG.chains
    const chain = chains.find((chain) => chain.id === chainId)
    if (!chain || chain.unsupported) {
      return
    }
    const suffixParams = {
      markId: props.info.id,
      chainId: chainId
    }
    if (showInviteCode.value) {
      suffixParams.invite = props.inviteCode
    }
    const dataSuffix = encodeToHex(suffixParams)
    try {
      const params = {
        productId: props.info.id,
        chainId: chainId,
        amount: props.amount
      }
      if (showInviteCode.value) {
        params.inviteNo = props.inviteCode
      }
      await productOrderCheck(params).then(async () => {
        try {
          const hash = await writeContract(CHAINS_CONFIG, {
            address: StakeContractAddress[chainId],
            abi: ContractAbi[chainId],
            functionName: 'stake',
            args: [toBigNumber(props.amount)],
            dataSuffix: `0x${dataSuffix}`
          })
          params.txnHash = hash

          await stakeCreate(params)
          userStore.getUserMsg()
        } catch (error) {
          handleWeb3Error(error)
          loading.value = false
          return
        }
        message.success(t('stakeSuc'))
        emit('success')
      })
    } catch (error) {
      handleWeb3Error(error)
      loading.value = false
      return
    }
  } catch (error) {
    //
  }
  loading.value = false
}
const doReinvest = async () => {
  try {
    // 0：收益 1：奖励
    const { chainId } = getAccount(CHAINS_CONFIG)
    const params = {
      productId: props.info.id,
      chainId: chainId,
      amount: props.amount,
      accountType: props.accountType
    }
    await productOrderReinvest(params)
    userStore.getUserMsg()
    message.success(t('stakeSuc'))
    emit('success')
  } catch (error) {
    handleWeb3Error(error)
  } finally {
    loading.value = false
  }
}
const onConfirm = debounce(async () => {
  if (loading.value) return
  loading.value = true

  if (props.type == 'reinvest') {
    doReinvest()
    return
  }
  callContract()
})

const dateStr = ref('')
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      if (locale.value == 'zh-CN') {
        dateStr.value = dayjs().format('YYYY/MM/DD HH:mm:ss')
      } else {
        dateStr.value = dayjs().format('DD/MM/YYYY HH:mm:ss')
      }
    }
  }
)
</script>

<template>
  <Teleport to="body">
    <Transition name="stake-confirm">
      <div v-if="visible" class="stake-confirm-modal-container">
        <div v-loading="loading" class="stake-confirm-box">
          <div class="stake-confirm-title">{{ $t('StakeStatus') }}</div>
          <div v-if="info" class="stake-confirm-content">
            <ul>
              <li>
                <label>{{ $t('StakingTime') }}:</label>
                <span>{{ dateStr }}</span>
              </li>
              <li>
                <label>{{ $t('StakeAmount') }}:</label>
                <span>{{ amount }} FNXAI</span>
              </li>
              <li>
                <label>{{ $t('StakingTermsMo') }}:</label>
                <span>{{ info.lockTime }} {{ $t('TradingDays') }}</span>
              </li>
              <li>
                <label>{{ $t('DailyPayoutRate') }}:</label>
                <span>{{ (info.stakeRate * 100).toFixed(2) }}%</span>
              </li>
              <li v-if="info.type != '2'">
                <label>{{ $t('TotalPayout') }}:</label>
                <span>{{ (info.stakeRate * info.lockTime * 100).toFixed(2) }}%</span>
              </li>
              <li v-else>
                <label>{{ $t('TotalPayout') }}:</label>
                <span>{{ (info.stakeRate * info.lockTime * 100 + 100).toFixed(2) }}%</span>
              </li>
              <li>
                <label>{{ $t('PrincipalReturn') }}:</label>
                <span v-if="info.type != '2'">{{ $t('IncludedIn') }}</span>
                <span v-else>{{ $t('IncludedIn2') }}</span>
              </li>
            </ul>
          </div>
          <div v-if="info.interestType == '1'" class="stake-confirm-note">
            <div class="stake-confirm-note-title">
              <img src="@/assets/images/s_tip.png" /><span>{{ $t('Note') }}</span>
            </div>
            <p>{{ noteTip }}</p>
          </div>
          <div class="stake-confirm-actions">
            <div @click="onCancel" class="stake-confirm-btn">{{ $t('Return') }}</div>
            <div @click="onConfirm" class="stake-confirm-btn stake-confirm-btn-primary">
              {{ $t('Stake') }}
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>
<style scoped>
.stake-confirm-modal-container {
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  --width: 383px;
  --radius: 15px;
  --padding: 26px 28px 20px;
  --title-bottom: 14px;
  --title-font-size: 17px;
  --content-padding: 17px 0;
  --content-font-size: 14px;
  --content-item-padding: 4px 0;
  --note-top: 16px;
  --note-title-bottom: 8px;
  --note-title-font-size: 13px;
  --note-content-font-size: 12px;
  --note-title-gap: 5px;
  --note-title-icon-width: 15px;
  --action-top: 35px;
  --action-gap: 20px;
  --btn-height: 43px;
  --btn-radius: 6px;
  --btn-font-size: 14px;
}
.stake-confirm-box {
  width: var(--width);
  border-radius: var(--radius);
  background-color: var(--modal-bg);
  padding: var(--padding);
}
.stake-confirm-title {
  padding-bottom: var(--title-bottom);
  border-bottom: 1px solid #474747;
  text-align: center;
  font-size: var(--title-font-size);
  font-weight: 600;
}
.stake-confirm-content {
  padding: var(--content-padding);
  border-bottom: 1px solid #474747;
}
.stake-confirm-content li {
  display: flex;
  align-items: center;
  font-size: var(--content-font-size);
}
ul li {
  padding: var(--content-item-padding);
}
li span {
  font-weight: 600;
  padding-left: 4px;
}

.stake-confirm-note {
  padding-top: var(--note-top);
}
.stake-confirm-note-title {
  display: flex;
  align-items: center;
  gap: var(--note-title-gap);
  padding-bottom: var(--note-title-bottom);
}
.stake-confirm-note-title img {
  width: var(--note-title-icon-width);
}
.stake-confirm-note-title span {
  font-size: var(--note-title-font-size);
}
.stake-confirm-note p {
  font-size: var(--note-content-font-size);
  color: #c4c4c4;
}
.stake-confirm-actions {
  margin-top: var(--action-top);
  display: flex;
  align-items: center;
  gap: var(--action-gap);
}
.stake-confirm-actions .stake-confirm-btn {
  flex: 1;
  height: var(--btn-height);
  border-radius: var(--btn-radius);
  border: 1px solid #737984;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--btn-font-size);
  font-weight: 600;
  cursor: pointer;
}
.stake-confirm-actions .stake-confirm-btn.stake-confirm-btn-primary {
  border-color: var(--primary-bg);
  background-color: var(--primary-bg);
}
.stake-confirm-btn:hover {
  opacity: 0.92;
}

.stake-confirm-enter-active,
.stake-confirm-leave-active {
  transition: opacity 0.5s ease;
}

.stake-confirm-enter-from,
.stake-confirm-leave-to {
  opacity: 0;
}
@media (max-width: 767px) {
  .stake-confirm-modal-container {
    --width: 323px;
    --radius: 15px;
    --padding: 25.5px 27.5px 20.5px;
    --title-bottom: 14.5px;
    --title-font-size: 17px;
    --content-padding: 17px 0;
    --content-font-size: 13.5px;
    --content-item-padding: 4px 0;
    --note-top: 16px;
    --note-title-bottom: 8px;
    --note-title-font-size: 13px;
    --note-content-font-size: 12px;
    --note-title-gap: 4.5px;
    --note-title-icon-width: 15px;
    --action-top: 20.5px;
    --action-gap: 12px;
    --btn-height: 43px;
    --btn-radius: 6px;
    --btn-font-size: 14px;
  }
}
</style>
