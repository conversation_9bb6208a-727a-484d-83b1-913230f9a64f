<script setup>
import { onMounted, ref } from 'vue'
import { getStakeList } from '@/api/stake'
import { getChainId, getAccount, switchChain, disconnect } from '@wagmi/core'
import { CHAINS, CHAINS_CONFIG } from '@/config'
import ConfirmModal from '@/components/ConfirmModal.vue'
import StakeModal from './components/StakeModal.vue'
import { isLogin, isMobileWeb } from '@/utils'
import { useRouter } from 'vue-router'
import { getConfig } from '@/api/public'
import { useUserStore } from '@/stores/user'
import { createModal } from '@/utils/web3-setup.js'
import { useAppKitAccount } from '@reown/appkit/vue'

const modal = createModal

const userStore = useUserStore()

const useAccount = useAppKitAccount()
const chains = CHAINS_CONFIG.chains

const loading = ref(true)
const stakeList = ref([])
const stakingTermsTip = ref('')
const dailyPayoutRateTip = ref('')
const totalStakedTip = ref('')

onMounted(async () => {
  getConfig('product_rule').then((res) => {
    stakingTermsTip.value = res.product_rule
  })
  getConfig('product_daily_rate').then((res) => {
    dailyPayoutRateTip.value = res.product_daily_rate
  })
  getConfig('product_total_amount').then((res) => {
    totalStakedTip.value = res.product_total_amount
  })
  try {
    const result = await getStakeList()
    stakeList.value = result
  } catch (error) {
    //
  }
  loading.value = false
})
const router = useRouter()
const stakeVisible = ref(false)
const confirmVisible = ref(false)
const nextChainId = ref()
const curChainName = ref()
const onStakeSwitchConfirm = async () => {
  confirmVisible.value = false
  try {
    await switchChain(CHAINS_CONFIG, {
      chainId: nextChainId.value
    })
  } catch (error) {
    console.log(error)
    return
  }
  if (isMobileWeb()) {
    router.push({
      name: 'stake'
    })
  } else {
    stakeVisible.value = true
  }
}

const toLogin = async () => {
  let { isConnected } = getAccount(CHAINS_CONFIG)

  if (!isConnected && !useAccount.value.isConnected) {
    const chainId = getChainId(CHAINS_CONFIG)
    const chain = chains.find((chain) => chain.id === chainId)

    if (chainId && !chain) {
      try {
        await switchChain(CHAINS_CONFIG, {
          chainId: CHAINS[0].id
        })
      } catch (error) {
        console.log('switchChainError:', error)
        return
      }
    }
  }

  if (isConnected || useAccount.value.isConnected) {
    try {
      await disconnect(CHAINS_CONFIG)
    } catch (error) {
      console.log('isConnected:', error)
    }
  }

  modal.open()
}

const onChainStake = (chainId) => {
  if (!isLogin()) {
    toLogin()
    return
  }
  userStore.getUserMsg()
  const { chain } = getAccount(CHAINS_CONFIG)

  nextChainId.value = chainId
  if (chain && chain.id != chainId) {
    curChainName.value = chain.name
    confirmVisible.value = true
  } else {
    if (isMobileWeb()) {
      router.push({
        name: 'stake'
      })
    } else {
      stakeVisible.value = true
    }
  }
}

const onEthStake = () => {
  onChainStake(CHAINS[0].id)
}
const onArbitrumStake = () => {
  onChainStake(CHAINS[1].id)
}
const onBnbStake = () => {
  onChainStake(CHAINS[2].id)
}

const stakeSuccess = () => {
  stakeVisible.value = false
  router.push({
    name: 'myStake'
  })
}
</script>

<template>
  <div class="home-container">
    <div v-loading="loading" class="home-stake-card">
      <div class="stake-top">
        <div class="stake-top-item">
          <img src="@/assets/images/eth.png" />
          <p>ETH</p>
          <div @click="onEthStake" class="stake-btn">{{ $t('Stake') }}</div>
        </div>
        <div class="stake-top-item">
          <img src="@/assets/images/arbitrum.png" />
          <p>ARBITRUM</p>
          <div @click="onArbitrumStake" class="stake-btn">{{ $t('Stake') }}</div>
        </div>
        <div class="stake-top-item">
          <img src="@/assets/images/bnb.png" />
          <p>BNB</p>
          <div @click="onBnbStake" class="stake-btn">{{ $t('Stake') }}</div>
        </div>
      </div>
      <div class="stake-subtitle-container">
        <div class="stake-subtitle-line"></div>
        <div class="stake-subtitle">FNXAI {{ $t('Staking') }}</div>
        <div class="stake-subtitle-line"></div>
      </div>
      <div class="stake-table">
        <div class="stake-table-head">
          <div class="stake-table-th">
            <div class="stake-table-th-label th-label-pc">{{ $t('StakingTerms') }}</div>
            <div class="stake-table-th-label th-label-mobile" v-html="$t('StakingTermsM')"></div>
            <el-tooltip
              :content="stakingTermsTip"
              placement="top"
              effect="customized"
              popper-class="home-popper"
              ><img src="@/assets/images/tip.png"
            /></el-tooltip>
          </div>
          <div class="stake-table-th">
            <div class="stake-table-th-label th-label-pc">{{ $t('DailyPayoutRate') }}</div>
            <div class="stake-table-th-label th-label-mobile" v-html="$t('DailyPayoutRateM')"></div>
            <el-tooltip
              :content="dailyPayoutRateTip"
              placement="top"
              effect="customized"
              popper-class="home-popper"
              ><img src="@/assets/images/tip.png"
            /></el-tooltip>
          </div>
          <div class="stake-table-th">
            <div class="stake-table-th-label th-label-pc">{{ $t('TotalStaked') }}</div>
            <div class="stake-table-th-label th-label-mobile" v-html="$t('TotalStakedM')"></div>
            <el-tooltip
              :content="totalStakedTip"
              placement="top"
              effect="customized"
              popper-class="home-popper"
              ><img src="@/assets/images/tip.png"
            /></el-tooltip>
          </div>
        </div>
        <div class="stake-table-body">
          <div
            v-for="item in stakeList"
            :key="item.id"
            class="stake-table-tr"
            :class="{ stop: item.stopFlag == '0' }"
          >
            <div class="stake-table-td">
              <div class="stake-value">
                {{ item.lockTime }}
                <div v-if="item.type == '1'" class="special-tip">
                  <img src="@/assets/images/special.png" /><span>{{ $t('special') }}</span>
                </div>
                <div v-else-if="item.type == '2'" class="special-tip">
                  <img src="@/assets/images/special.png" /><span>New</span>
                </div>
              </div>
              <div class="stake-label">{{ $t('Days') }}</div>
            </div>
            <div class="stake-table-td">
              <div class="stake-value">{{ (item.dailyInterestRate * 100).toFixed(2) }}%</div>
              <div class="stake-label">{{ $t('Daily') }}</div>
            </div>
            <div class="stake-table-td">
              <div class="stake-value">{{ item.totalAmount }}</div>
              <div class="stake-label">FNXAI</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <confirm-modal
      :visible="confirmVisible"
      :title="$t('Reminder')"
      :content="$t('StakeSwitchTip', { chain: curChainName })"
      @confirm="onStakeSwitchConfirm"
      @cancel="confirmVisible = false"
    />
    <stake-modal :visible="stakeVisible" @cancel="stakeVisible = false" @success="stakeSuccess" />
  </div>
</template>

<style scoped>
.home-container {
  width: var(--content-width);
  margin: 0 auto;
  padding-top: calc(var(--header-height) + 180px);
  padding-bottom: 50px;
  min-height: calc(100vh - var(--footer-height));
  background-repeat: no-repeat;
  background-position: top center;
  background-size: calc(var(--content-width) + 10px);
  --card-radius: 33px;
  --card-padding: 29px 161px 9px;
  --card-top-icon-width: 121px;
  --card-top-item-label-top: 10px;
  --card-top-item-label-font-size: 20px;
  --card-top-btn-top: 13px;
  --card-top-btn-radius: 12px;
  --card-top-btn-height: 56px;
  --card-top-btn-width: 158px;
  --card-top-btn-font-size: 22px;

  --subtitle-top: 46px;
  --subtitle-font-size: 18px;
  --subtitle-padding: 0 75px;

  --table-padding: 0 70px;
  --table-line-color: #505057;
  --table-top: 26px;
  --table-head-bottom: 12px;
  --table-title-font-size: 14px;
  --table-tip-left: 5px;
  --table-tip-width: 18px;
  --table-tr-padding-top: 11px;
  --table-tr-padding-bottom: 11px;
  --table-tr-value-font-size: 20px;
  --table-tr-lable-font-size: 14px;

  --stake-table-td-value-text-color: #fff;
  --stake-table-td-value-text-stop-color: rgba(255, 255, 255, 0.3);

  --stake-special-tip-ml: 5px;
  --stake-special-pd: 2px 6px;
  --stake-special-gap: 2px;
  --stake-special-br: 10px 10px 10px 2px;
  --stake-special-fs: 12px;
  --stake-special-top: -3px;
}
.home-stake-card {
  width: 100%;
  padding: var(--card-padding);
  border-radius: var(--card-radius);
  background-color: rgba(50, 56, 68, 0.71);
  border-image: linear-gradient(180deg, rgba(200, 200, 200, 0.23), rgba(117, 131, 136, 0.48)) 2 2;
  backdrop-filter: blur(10px);
  border: 2px solid #758388;
  font-size: 0;
  overflow: hidden;
}

.stake-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.stake-top .stake-top-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stake-top-item img {
  width: var(--card-top-icon-width);
}
.stake-top-item p {
  margin-top: var(--card-top-item-label-top);
  font-size: var(--card-top-item-label-font-size);
  font-weight: 600;
}
.stake-top-item .stake-btn {
  margin-top: var(--card-top-btn-top);
  height: var(--card-top-btn-height);
  width: var(--card-top-btn-width);
  border-radius: var(--card-top-btn-radius);
  font-size: var(--card-top-btn-font-size);
  font-weight: 600;
  background-color: #13e6bc;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.stake-top-item .stake-btn:hover {
  opacity: 0.92;
}

.stake-subtitle-container {
  display: flex;
  align-items: center;
  margin-top: var(--subtitle-top);
}
.stake-subtitle-line {
  flex: 1;
  height: 1px;
  background-color: var(--color-tertiary-text);
}
.stake-subtitle {
  padding: var(--subtitle-padding);
  font-size: var(--subtitle-font-size);
  font-weight: 600;
}

.stake-table {
  margin-top: var(--table-top);
}
.stake-table-head {
  width: 100%;
  display: flex;
  align-items: center;
  padding: var(--table-padding);
  padding-bottom: var(--table-head-bottom);
  border-bottom: 1px solid var(--table-line-color);
}
.stake-table-th {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}
.stake-table-th .stake-table-th-label {
  font-size: var(--table-title-font-size);
  color: var(--color-tertiary-text);
}
.stake-table-th-label.th-label-mobile {
  display: none;
}
.stake-table-th img {
  width: var(--table-tip-width);
  margin-left: var(--table-tip-left);
  cursor: pointer;
}

.stake-table-tr {
  display: flex;
  align-items: center;
  padding: var(--table-padding);
  padding-top: var(--table-tr-padding-top);
  padding-bottom: var(--table-tr-padding-bottom);
}
.stake-table-tr:not(:last-child) {
  border-bottom: 1px solid var(--table-line-color);
}
.stake-table-tr .stake-table-td {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stake-table-tr .stake-table-td:not(:last-child) {
  padding-right: calc(var(--table-tip-width) + var(--table-tip-left));
}
.stake-table-td .stake-value {
  font-size: var(--table-tr-value-font-size);
  font-weight: 600;
  color: var(--stake-table-td-value-text-color);
  position: relative;
}
.stake-table-td .stake-value .special-tip {
  white-space: nowrap;
  margin-left: var(--stake-special-tip-ml);
  padding: var(--stake-special-pd);
  display: flex;
  align-items: center;
  gap: var(--stake-special-gap);
  background: linear-gradient(270deg, #b1d106 0%, #e4c60e 100%);
  border-radius: var(--stake-special-br);
  font-size: var(--stake-special-fs);
  position: absolute;
  top: var(--stake-special-top);
  left: 100%;
}
.stake-table-td .stake-value .special-tip img {
  height: 1em;
}
.stake-table-td .stake-value .special-tip span {
  font-weight: 500;
  color: #ffffff;
}
.stake-table-tr.stop .stake-table-td .stake-value {
  color: var(--stake-table-td-value-text-stop-color);
}
.stake-table-td .stake-label {
  font-size: var(--table-tr-lable-font-size);
  color: var(--color-tertiary-text);
}

@media (min-width: 1536px) {
  .home-container {
    --card-padding: 34px 161px 14px;
  }
}
@media (max-width: 1024px) {
  .home-container {
    background-size: var(--content-width);
    padding-top: calc(var(--header-height) + 184px);
    padding-bottom: 30px;
    --card-radius: 18px;
    --card-padding: 30px 60px 18.5px;
    --card-top-icon-width: 110px;
    --card-top-item-label-top: 9px;
    --card-top-item-label-font-size: 17px;
    --card-top-btn-top: 15px;
    --card-top-btn-radius: 8px;
    --card-top-btn-height: 42px;
    --card-top-btn-width: 110px;
    --card-top-btn-font-size: 17px;

    --subtitle-top: 32px;
    --subtitle-font-size: 16px;
    --subtitle-padding: 0 40px;

    --table-padding: 0;
    --table-top: 17.5px;
    --table-head-bottom: 10px;
    --table-title-font-size: 11px;
    --table-tip-left: 1px;
    --table-tip-width: 15px;
    --table-tr-padding-top: 17px;
    --table-tr-padding-bottom: 15.5px;
    --table-tr-value-font-size: 16px;
    --table-tr-lable-font-size: 11px;

    --stake-special-tip-ml: 4px;
    --stake-special-pd: 2px 6px;
    --stake-special-gap: 2px;
    --stake-special-br: 8px 8px 8px 2px;
    --stake-special-fs: 10px;
    --stake-special-top: -3px;
  }
  .home-stake-card {
    margin: 0 25px;
    width: auto;
    border-width: 1px;
  }
  .stake-top {
    padding: 0 15px;
  }
}
@media (max-width: 767px) {
  .home-container {
    padding-top: calc(var(--header-height) + 184px);
    padding-bottom: 0;
    --card-radius: 16.5px;
    --card-padding: 28.5px 13.5px 17px;
    --card-top-icon-width: 79px;
    --card-top-item-label-top: 7.5px;
    --card-top-item-label-font-size: 15px;
    --card-top-btn-top: 17.5px;
    --card-top-btn-radius: 6px;
    --card-top-btn-height: 37px;
    --card-top-btn-width: 79px;
    --card-top-btn-font-size: 13px;

    --subtitle-top: 28.5px;
    --subtitle-font-size: 13px;
    --subtitle-padding: 0 22.5px;

    --table-padding: 0;
    --table-top: 17.5px;
    --table-head-bottom: 10px;
    --table-title-font-size: 11px;
    --table-tip-left: 3px;
    --table-tip-width: 18px;
    --table-tr-padding-top: 17px;
    --table-tr-padding-bottom: 15.5px;
    --table-tr-value-font-size: 16px;
    --table-tr-lable-font-size: 11px;

    --stake-special-tip-ml: 2px;
    --stake-special-pd: 2px 4px;
    --stake-special-gap: 1px;
    --stake-special-br: 10px 10px 10px 2px;
    --stake-special-fs: 11px;
    --stake-special-top: -10px;
  }
  .home-stake-card {
    margin: 0 15px;
    width: auto;
    border-width: 1px;
  }
  .stake-top {
    padding: 0 11.5px;
  }
  .stake-table-th-label.th-label-pc {
    display: none;
  }
  .stake-table-th-label.th-label-mobile {
    display: block;
    text-align: center;
  }
  .stake-table-tr .stake-table-td:last-child {
    padding-right: calc(var(--table-tip-width) + var(--table-tip-left));
  }
}
</style>
<style>
.en .home-container {
  background-image: url('@/assets/images/home_bg.png');
}
.zh .home-container {
  background-image: url('@/assets/images/home_bg_zh.png');
}
.ko .home-container {
  background-image: url('@/assets/images/home_bg_ko.png');
}
@media (max-width: 767px) {
  .en .home-container {
    background-image: url('@/assets/images/home_bg_m.png');
  }
  .zh .home-container {
    background-image: url('@/assets/images/home_bg_m_zh.png');
  }
  .ko .home-container {
    background-image: url('@/assets/images/home_bg_m_ko.png');
  }
}
</style>
