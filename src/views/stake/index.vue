<script setup>
import {
  TokenContractAddress,
  CHAINS,
  ContractAbi,
  StakeContractAddress,
  CHAINS_CONFIG
} from '@/config'
import {
  disconnect,
  getAccount,
  readContract,
  switchChain,
  watchAccount,
  waitForTransaction,
  writeContract
} from '@wagmi/core'
import { computed, nextTick, onMounted, onUnmounted, ref } from 'vue'
import ConfirmModal from '@/components/ConfirmModal.vue'
import StakeConfirmModal from '@/views/home/<USER>/StakeConfirmModal.vue'
import StakeListModal from './components/StakeListModal.vue'
import { getMarketPrice, getStakeList } from '@/api/stake'
import { checkInviteCode, getMyAccount, getProductOrderSumAmount } from '@/api/user'
import { debounce } from 'lodash'
import { getConfig } from '@/api/public'
import { useUserStore } from '@/stores/user'
import {
  formatAmount,
  formatBigMumber,
  formatPrice,
  handleWeb3Error,
  isGreater,
  toBigNumber
} from '@/utils'
import BigNumber from 'bignumber.js'
import { useRoute, useRouter } from 'vue-router'
import message from '@/utils/message.js'
import { useI18n } from 'vue-i18n'
import { createModal } from '@/utils/web3-setup.js'

const { t } = useI18n()
const route = useRoute()
const type = route.query.type || 'stake'
const accountType = route.query.accountType || '0'

const noteTip = ref('')
const price = ref('0.00') // market price
const amount = ref('') // stake amount
const balance = ref('0.00')
const stakeList = ref([])
const choseStakeIndex = ref(-1)

const stakeInfo = computed(() => {
  if (stakeList.value.length && choseStakeIndex.value > -1) {
    return stakeList.value[choseStakeIndex.value]
  }
  return null
})

const stakeUsdt = computed(() => {
  const pAmount = Number(amount.value)
  if (isNaN(pAmount)) {
    return '0.00'
  }
  const pPrice = Number(price.value)
  if (isNaN(pPrice)) {
    return '0.00'
  }
  return (pPrice * pAmount).toFixed(4).toString().replace(/0+$/, '').replace(/\.$/, '.00')
})

// 根据 stakeUsdt 匹配对应的 stakeRate
const stakeRate = computed(() => {
  const val = Number(stakeUsdt.value)
  if (!val) return stakeInfo.value?.dailyInterestRate ?? 0
  // 找到满足条件的最大 minStakeAmount
  const tier = [...(stakeInfo.value?.stakingTiers ?? [])]
    .sort((a, b) => b.minStakeAmount - a.minStakeAmount) // 倒序
    .find(item => val >= item.minStakeAmount)
  return tier ? tier.stakeRate : (stakeInfo.value?.dailyInterestRate ?? 0)
})

const stakeContainer = ref()
const inputVal = ref()
const calcWidth = () => {
  nextTick(() => {
    if (!amount.value.length) {
      stakeContainer.value.style.setProperty('--amount-unit-left', '-999px')
      return
    }
    const style = window.getComputedStyle(inputVal.value)
    const width = style.getPropertyValue('width')
    stakeContainer.value.style.setProperty('--amount-unit-left', width)
  })
}

const handleBlur = () => {
  const num = Number(amount.value)
  if (isNaN(num)) {
    if (stakeList.value.length) {
      const info = stakeList.value[choseStakeIndex.value]
      amount.value = info.initialCapital.toString() || ''
    } else {
      amount.value = ''
    }
  }
  calcWidth()
}

const onMaxClick = () => {
  amount.value = formatAmount(balance.value.toString(), 0)
  calcWidth()
}

let balanceTimer
const refreshBalance = async () => {
  const { address, chainId } = getAccount(CHAINS_CONFIG)

  const chains = CHAINS_CONFIG.chains
  const chain = chains.find((chain) => chain.id === chainId)
  if (address) {
    let cId = chainId
    if (chainId && !chain) {
      cId = CHAINS[0].id
    }
    const result = await readContract(CHAINS_CONFIG, {
      address: TokenContractAddress[cId],
      abi: ContractAbi[cId],
      functionName: 'balanceOf',
      args: [address]
    })
    balance.value = formatPrice(formatBigMumber(result))
  }
}

const listVisible = ref(false)
const currentChainId = ref()
const getChain = ref()

const onChoseToggle = () => {
  listVisible.value = !listVisible.value
}
const choseStake = (index) => {
  choseStakeIndex.value = index
  listVisible.value = false
}

const inviteVisible = ref(false)
const inviteCode = ref('')
const inviteStatus = ref(0) // 0 init, 1 suc, -1 error
const checkCode = debounce(async () => {
  if (!inviteCode.value) {
    inviteStatus.value = 0
    try {
      const result = await getStakeList()

      stakeList.value = result
    } catch (error) {
      //
    }
    return
  }
  checkInviteCode({
    inviteCode: inviteCode.value
  }).then(async (res) => {
    if (res.flag == '1') {
      inviteStatus.value = 1
      try {
        const result = await getStakeList({
          inviteNo: inviteCode.value
        })

        stakeList.value = result
      } catch (error) {
        //
      }
    } else {
      inviteStatus.value = -1
    }
  })
}, 400)
const loading = ref(false)
const stakeConfirmVisible = ref(false)
const btnDisabled = computed(() => {
  if (
    !stakeInfo.value ||
    (inviteCode.value && inviteStatus.value != 1) ||
    stakeInfo.value?.stopFlag === '0'
  ) {
    return true
  }
  if (stakeInfo.value.type == '1') {
    if (stakeInfo.value.remainTime < 1 && stakeInfo.value.remainTime != -1) {
      return true
    }
  }
  const pAmount = Number(amount.value)
  if (isNaN(pAmount)) {
    return true
  }
  const initialCapital = Number(stakeInfo.value.initialCapital)
  if (pAmount < initialCapital) {
    return true
  }
  if (stakeInfo.value.type == '1') {
    const maxCapital = Number(stakeInfo.value.maxCapital)
    if (pAmount > maxCapital) {
      return true
    }
  }
  return false
})

const userStore = useUserStore()
const modal = createModal

const showInviteCode = computed(() => {
  return userStore.user && userStore.user.productFlag == '0'
})

const onConfirm = async () => {
  if (btnDisabled.value || loading.value) return
  if (!inviteCode.value && showInviteCode.value) {
    inviteVisible.value = true
    return
  }

  // stopFlag标识0:停止募集 1:募集中
  if (stakeList.value[choseStakeIndex.value].stopFlag != '1') {
    message.warning(t('Cannot stake during the fundraising period'))
    return
  }

  doStake()
}
const doStake = async () => {
  loading.value = true
  const { chainId, address, isConnected } = getAccount(CHAINS_CONFIG)
  const chain = CHAINS_CONFIG.chains.find((chain) => chain.id === chainId)
  if (!chain) {
    try {
      await switchChain(CHAINS_CONFIG, {
        chainId: CHAINS[0].id
      })
    } catch (error) {
      loading.value = false
      return
    }
  }
  if (!isConnected || !userStore.user || address != userStore.user.address) {
    // not login
    if (isConnected) {
      try {
        await disconnect()
      } catch (error) {
        //
      }
      modal.open()
    } else {
      modal.open()
    }
    loading.value = false
  } else {
    if (chain.id != CHAINS[0].id) {
      try {
        const allowance = await readContract(CHAINS_CONFIG, {
          address: TokenContractAddress[chain.id],
          abi: ContractAbi[chain.id],
          functionName: 'allowance',
          args: [address, StakeContractAddress[chain.id]]
        })
        // 质押数量 > allowance
        if (isGreater(toBigNumber(amount.value), allowance.toString())) {
          const hash = await writeContract(CHAINS_CONFIG, {
            address: TokenContractAddress[chain.id],
            abi: ContractAbi[chain.id],
            functionName: 'approve',
            args: [
              StakeContractAddress[chain.id],
              '1157920892373161954235709850086879078532699846656405640394575840079131'
            ]
          })
          const result = await waitForTransaction(CHAINS_CONFIG, {
            chainId: chain.id,
            hash
          })
          if (result.status == 'success') {
            stakeConfirmVisible.value = true
          }
        } else {
          stakeConfirmVisible.value = true
        }
      } catch (error) {
        handleWeb3Error(error)
        loading.value = false
      }
    } else {
      stakeConfirmVisible.value = true
    }
  }
}
const afterInviteConfirm = () => {
  inviteVisible.value = false
  doStake()
}
const onStakeConfirmCancel = () => {
  stakeConfirmVisible.value = false
  loading.value = false
}
const router = useRouter()
const stakeSuccess = () => {
  stakeConfirmVisible.value = false
  router.push({
    name: 'myStake'
  })
}

const unwatchAccount = watchAccount(CHAINS_CONFIG, {
  onChange(network, prevNetwork) {
    if (network.chainId && network.chainId != prevNetwork.chainId) {
      const chains = CHAINS_CONFIG.chains
      const chain = chains.find((chain) => chain.id === network.chainId)
      if (network.chainId && !chain) {
        currentChainId.value = CHAINS[0].id
      } else {
        currentChainId.value = chain.id
      }
      stakeConfirmVisible.value = false
      amount.value = ''
      calcWidth()
    }
  }
})
const getAvailableBalance = async () => {
  let { chainId } = getAccount(CHAINS_CONFIG)
  const chain = CHAINS_CONFIG.chains.find((chain) => chain.id === chainId)
  if (chainId && !chain) {
    chainId = CHAINS[0].id
  }
  const res = await getProductOrderSumAmount({ chainId })
  balance.value = res.totalAmount
}
const getTeamBalance = async () => {
  const res = await getMyAccount()
  if (userStore.user.accountStatus == '2') {
    balance.value = Number((Number(res.lockAmount) + Number(res.settleAmount)).toFixed(10))
  } else {
    balance.value = res.lockAmount
  }
}
onMounted(async () => {
  await userStore.getUserMsg()
  if (type == 'reinvest') {
    if (accountType == '0') {
      getAvailableBalance()
    } else {
      getTeamBalance()
    }
  } else {
    refreshBalance()
    balanceTimer = setInterval(refreshBalance, 30 * 1000)
  }
  try {
    getConfig('product_note').then((res) => {
      noteTip.value = res.product_note || ''
    })
    getMarketPrice().then((res) => {
      price.value = res.lastPrice
    })
    const result = await getStakeList()
    let flag = false
    result
      .map((item, index) => {
        if (item.stopFlag != '0' && !flag) {
          choseStakeIndex.value = index
          flag = true
        }
      })
      .slice()

    stakeList.value = result
  } catch (error) {
    //
  }
  const { chainId } = getAccount(CHAINS_CONFIG)

  const chains = CHAINS_CONFIG.chains
  const chain = chains.find((chain) => chain.id === chainId)

  if (chainId && !chain) {
    currentChainId.value = CHAINS[0].id
  } else {
    currentChainId.value = chain.id
  }
  getChain.value = chain
})
onUnmounted(() => {
  unwatchAccount()
  if (balanceTimer) {
    clearInterval(balanceTimer)
    balanceTimer = undefined
  }
})

const amountInput = (v) => {
  let inputValue = formatAmount(v.target.value, 0)

  const a = new BigNumber(inputValue)
  const b = new BigNumber(balance.value.toString())

  // 检查输入是否有效
  if (inputValue === '' || isNaN(a.toNumber())) {
    amount.value = inputValue.toString() // 允许清空输入
    calcWidth()
    return
  }
  let rAmount
  if (a.isGreaterThan(b)) {
    rAmount = formatAmount(balance.value.toString(), 0)
  } else {
    rAmount = inputValue.toString()
  }
  if (stakeInfo.value.type == '1') {
    const maxCapital = new BigNumber(stakeInfo.value.maxCapital)
    if (a.isGreaterThan(maxCapital)) {
      rAmount = formatAmount(stakeInfo.value.maxCapital.toString(), 0)
    }
  }
  amount.value = rAmount
  calcWidth()
}
</script>

<template>
  <div class="stake-page-container" ref="stakeContainer">
    <div class="stake-content">
      <div class="stake-top-info">
        <div class="stake-network">
          <span>{{ $t('Stakeon') }}</span>
          <div class="network-icon">
            <img v-if="currentChainId == CHAINS[0].id" src="@/assets/images/eth_s.png" />
            <img v-if="currentChainId == CHAINS[1].id" src="@/assets/images/arbitrum_s.png" />
            <img v-if="currentChainId == CHAINS[2].id" src="@/assets/images/bnb_s.png" />
          </div>
        </div>
        <div class="market-price">
          <img src="@/assets/images/coin_icon.png" />
          <span>{{ $t('Price') }}: {{ price }}USDT</span>
        </div>
        <div class="stake-amount-box">
          <div class="stake-amount">
            <input
              v-model="amount"
              @blur="handleBlur"
              :placeholder="$t('EnterStakeAmount')"
              @input="amountInput"
            />
            <div class="unit">FNXAI</div>
            <div @click="onMaxClick" class="max-btn">{{ $t('Max') }}</div>
            <div ref="inputVal" class="input-val">{{ amount }}</div>
          </div>
          <p v-if="stakeInfo" class="min-stake-tip">
            {{ $t('MinStakingAmount') }}: {{ stakeInfo.initialCapital }} FNXAI
          </p>
          <p v-else class="min-stake-tip">{{ $t('MinStakingAmount') }}: - FNXAI</p>
        </div>
        <div class="balance-box">
          <div class="stake-usdt">{{ stakeUsdt }}USDT</div>
          <div class="balance">
            <img src="@/assets/images/coin_icon.png" /><span
              >{{ $t('Balance') }}:{{ balance }}</span
            >
          </div>
        </div>
      </div>
      <div v-if="stakeInfo" class="stake-term">
        <div class="term-box">
          <div class="term-title">{{ $t('StakeTerms') }}</div>
          <div
            @click="onChoseToggle"
            class="term-chose"
            :class="{
              'chose-open': listVisible
            }"
          >
            <span>{{ stakeInfo.lockTime }} {{ $t('Days') }}</span
            ><img v-if="stakeList.length > 1" src="@/assets/images/arrow_down.png" />
          </div>
        </div>
        <div class="term-info">
          <p>{{ $t('DailyPayoutRate') }}: {{ (stakeRate * 100).toFixed(2) }}%</p>
          <p v-if="stakeInfo.type != '2'">
            {{ $t('TotalPayout') }}:
            {{ (stakeRate * 100 * stakeInfo.lockTime).toFixed(2) }}%
          </p>
          <p v-else>
            {{ $t('TotalPayout') }}:
            {{ (stakeRate * 100 * stakeInfo.lockTime + 100).toFixed(2) }}%
            <span>({{ $t('IncludingPrincipal') }})</span>
          </p>
          <p v-if="stakeInfo.type != '2'">{{ $t('PrincipalReturnLong') }}</p>
          <p v-else>{{ $t('PrincipalReturnLong2') }}</p>
        </div>
      </div>
      <div v-if="stakeInfo?.interestType == '1'" class="stake-note">
        <div class="stake-note-title">
          <img src="@/assets/images/s_tip.png" /><span>{{ $t('Note') }}</span>
        </div>
        <p>{{ noteTip }}</p>
      </div>
      <div
        v-if="showInviteCode"
        class="stake-invite"
        :class="{
          'stake-invite-suc': inviteStatus == 1,
          'stake-invite-error': inviteStatus == -1
        }"
      >
        <label>{{ $t('InvitationCode') }}</label>
        <input v-model="inviteCode" @input="checkCode" :placeholder="$t('Enter')" />
        <img class="check-suc" src="@/assets/images/check_suc.png" />
      </div>
      <div class="stake-invite-error-tip">{{ $t('EnterValidCode') }}</div>
    </div>
    <div @click="onConfirm" :disabled="btnDisabled" class="stake-btn">
      {{ stakeInfo?.stopFlag == '0' ? $t('Currently not available') : $t('Stake') }}
    </div>
    <ConfirmModal
      :visible="inviteVisible"
      :content="$t('NoInviteTip')"
      :ok-text="$t('ContinueToStake')"
      large-btn
      @cancel="inviteVisible = false"
      @confirm="afterInviteConfirm"
    />
    <StakeConfirmModal
      :visible="stakeConfirmVisible"
      :info="{...stakeInfo, stakeUsdt, stakeRate}"
      :amount="amount"
      :inviteCode="inviteCode"
      :noteTip="noteTip"
      :type="type"
      :accountType="accountType"
      @cancel="onStakeConfirmCancel"
      @success="stakeSuccess"
    />
    <StakeListModal
      :visible="listVisible"
      :choseStakeIndex="choseStakeIndex"
      :stakeList="stakeList"
      @cancel="listVisible = false"
      @confirm="choseStake"
    />
  </div>
</template>
<style lang="scss" scoped>
@use 'sass:color';

.stake-page-container {
  padding: 3px 15px 45px;
  min-height: calc(100vh - var(--footer-height));
  padding-top: calc(var(--header-height) + 3px);
  font-size: 0;
  --btn-top: 16.5px;
  --btn-radius: 6px;
  --btn-height: 43px;
  --btn-font-size: 14px;
  --top-info-height: 185.5px;
  --top-info-padding: 0 15px;
  --network-top: 10px;
  --network-left: 22px;
  --network-font-size: 15px;
  --network-icon-left: 5px;
  --network-icon-width: 21px;
  --price-icon-width: 14px;
  --price-icon-right: 4px;
  --price-font-size: 11px;
  --price-top: 13px;
  --amount-top: 42px;
  --amount-bottom: 12.5px;
  --amount-gap: 2px;
  --amount-input-height: 35px;
  --amount-input-font-size: 25px;
  --amount-max-btn-height: 27px;
  --amount-max-btn-font-size: 13px;
  --amount-max-btn-radius: 4px;
  --amount-max-btn-padding: 0 10px;
  --min-stake-tip-top: 5.5px;
  --min-stake-tip-font-size: 12px;
  --balance-top: 14.5px;
  --balance-font-size: 12px;
  --term-top: 10px;
  --term-radius: 16.5px;
  --term-padding: 3px 15px 11px;
  --term-box-padding: 19.5px 0;
  --term-box-font-size: 15px;
  --term-box-icon-width: 17px;
  --term-info-top: 8.5px;
  --term-info-font-size: 13px;
  --stake-note-top: 11.5px;
  --stake-note-title-bottom: 7px;
  --stake-note-font-size: 13px;
  --stake-note-title-gap: 4.5px;
  --stake-note-title-icon-width: 15px;
  --invite-top: 12.5px;
  --invite-radius: 16.5px;
  --invite-height: 61px;
  --invite-padding: 15px;
  --invite-gap: 5px;
  --invite-font-size: 15px;
  --invite-label-right: 15px;
  --invite-error-top: 7px;
  --invite-error-font-size: 12px;
  --invite-suc-icon-width: 17px;
  --amount-unit-left: -999px;
}
.stake-top-info {
  height: var(--top-info-height);
  background-image: url('@/assets/images/stake_bg_m.png');
  background-size: 100% 100%;
  position: relative;
  padding: var(--top-info-padding);
}
.stake-network {
  position: absolute;
  top: var(--network-top);
  left: var(--network-left);
  font-size: 0;
  display: flex;
  align-items: center;
}
.stake-network span {
  font-size: var(--network-font-size);
  font-weight: 600;
}
.stake-network .network-icon {
  margin-left: var(--network-icon-left);
  border-radius: calc(var(--network-icon-width) / 2);
  width: var(--network-icon-width);
  overflow: hidden;
}
.stake-network img {
  width: var(--network-icon-width);
}
.market-price {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-size: 0;
  padding-top: var(--price-top);
}
.market-price img {
  width: var(--price-icon-width);
  margin-right: var(--price-icon-right);
}
.market-price span {
  font-size: var(--price-font-size);
  padding-top: 1px;
}
.stake-amount-box {
  padding-top: var(--amount-top);
  padding-bottom: var(--amount-bottom);
  border-bottom: 1px solid #3e494d;
}
.stake-amount-box .stake-amount {
  display: flex;
  align-items: center;
  gap: var(--amount-gap);
  position: relative;
}
.stake-amount input {
  flex: 1;
  width: 100%;
  height: var(--amount-input-height);
  font-size: var(--amount-input-font-size);
  font-weight: 600;
}
.stake-amount .max-btn {
  height: var(--amount-max-btn-height);
  font-size: var(--amount-max-btn-font-size);
  border-radius: var(--amount-max-btn-radius);
  padding: var(--amount-max-btn-padding);
  background-color: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  white-space: nowrap;
  flex-shrink: 0;
}
.stake-amount .input-val {
  font-size: var(--amount-input-font-size);
  font-weight: 600;
  position: absolute;
  visibility: hidden;
}
.stake-amount .unit {
  font-size: var(--term-box-font-size);
  margin: 0 5px;
  position: absolute;
  left: var(--amount-unit-left);
}
.stake-amount .max-btn:hover {
  opacity: 0.92;
}
.min-stake-tip {
  margin-top: var(--min-stake-tip-top);
  font-size: var(--min-stake-tip-font-size);
  color: var(--color-tertiary-text);
}
.balance-box {
  padding-top: var(--balance-top);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.balance-box .stake-usdt {
  font-size: var(--balance-font-size);
}
.balance-box .balance {
  display: flex;
  align-items: center;
}
.balance-box .balance img {
  width: var(--price-icon-width);
  margin-right: var(--price-icon-right);
}
.balance-box .balance span {
  font-size: var(--price-font-size);
}
.stake-term {
  margin-top: var(--term-top);
  padding: var(--term-padding);
  background: rgba(50, 56, 68, 0.71);
  border-radius: var(--term-radius);
  border: 1px solid #5a6164;
}
.stake-term .term-box {
  padding: var(--term-box-padding);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #3e494d;
  position: relative;
}
.term-box .term-title {
  font-size: var(--term-box-font-size);
}
.term-box .term-chose {
  display: flex;
  align-items: center;
  cursor: pointer;
}
.term-box .term-chose span {
  font-size: var(--term-box-font-size);
  font-weight: 600;
}
.term-box .term-chose img {
  width: var(--term-box-icon-width);
  transition: 0.2s ease;
}
.term-chose.chose-open img {
  transform: rotateZ(180deg);
}
.term-info {
  padding-top: var(--term-info-top);
}
.term-info p {
  color: #b3b3b3;
  font-size: var(--term-info-font-size);
  line-height: 1.92;
}
.stake-note {
  padding-top: var(--stake-note-top);
}
.stake-note-title {
  display: flex;
  align-items: center;
  gap: var(--stake-note-title-gap);
  padding-bottom: var(--stake-note-title-bottom);
}
.stake-note-title img {
  width: var(--stake-note-title-icon-width);
}
.stake-note-title span {
  font-size: var(--stake-note-font-size);
}
.stake-note p {
  font-size: var(--stake-note-font-size);
  color: #c4c4c4;
}
.stake-invite {
  margin-top: var(--invite-top);
  border-radius: var(--invite-radius);
  border: 1px solid #5a6164;
  background: rgba(50, 56, 68, 0.71);
  height: var(--invite-height);
  display: flex;
  align-items: center;
  gap: var(--invite-gap);
  padding: var(--invite-padding);
}
.stake-invite label {
  padding-right: var(--invite-label-right);
}
.stake-invite label,
.stake-invite input {
  font-size: var(--invite-font-size);
}
.stake-invite input {
  flex: 1;
  height: 100%;
  text-align: right;
}
.stake-invite.stake-invite-error {
  border-color: var(--color-error);
}
.stake-invite.stake-invite-error label {
  color: var(--color-error);
}
.stake-invite-error-tip {
  display: none;
  font-size: var(--invite-error-font-size);
  margin-top: var(--invite-error-top);
  color: var(--color-error);
}
.stake-invite.stake-invite-error + .stake-invite-error-tip {
  display: block;
}
.stake-invite .check-suc {
  display: none;
  width: var(--invite-suc-icon-width);
}
.stake-invite.stake-invite-suc .check-suc {
  display: block;
}
.stake-btn {
  margin-top: var(--btn-top);
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--btn-height);
  border-radius: var(--btn-radius);
  font-size: var(--btn-font-size);
  background-color: var(--primary-bg);
  color: #fff;
  font-weight: 600;
  cursor: pointer;
  &[disabled='true'] {
    background-color: color.adjust(#ff3737, $lightness: 20%);
    cursor: not-allowed;
    &:hover {
      opacity: 1;
    }
  }
}
.stake-btn:hover {
  opacity: 0.92;
}

@media (min-width: 767px) {
  .stake-page-container {
    opacity: 0;
  }
}

.stake-enter-active,
.stake-leave-active {
  transition: opacity 0.5s ease;
}

.stake-enter-from,
.stake-leave-to {
  opacity: 0;
}
</style>
