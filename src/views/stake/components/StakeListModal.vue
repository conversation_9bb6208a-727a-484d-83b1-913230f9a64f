<script setup>
import { ref } from 'vue'

defineProps({
  visible: Boolean,
  choseStakeIndex: Number,
  stakeList: Array
})

const loading = ref(false)
const emit = defineEmits(['cancel', 'confirm'])

const onCancel = () => {
  emit('cancel')
}
const choseStake = (item, index) => {
  emit('confirm', index)
}
</script>

<template>
  <Teleport to="body">
    <Transition name="stake-list">
      <div v-if="visible" class="stake-list-modal-container">
        <div v-loading="loading" class="stake-list-box">
          <div class="stake-list-title">{{ $t('Stake') }}</div>
          <ul class="stake-list-content">
            <li
              v-for="(item, index) in stakeList"
              @click="choseStake(item, index)"
              :key="item.id"
              :class="{
                active: choseStakeIndex == index,
                stop: item.stopFlag == '0'
              }"
            >
              <div class="lock-day">
                {{ item.lockTime }} {{ $t('Days') }}
                <div v-if="item.type == '1'" class="special-tip">
                  <img src="@/assets/images/special.png" /><span>{{ $t('special') }}</span>
                </div>
                <div v-else-if="item.type == '2'" class="special-tip">
                  <img src="@/assets/images/special.png" /><span>New</span>
                </div>
              </div>
              <!-- -1 无限次 -->
              <div v-if="item.type == '1' && item.remainTime != -1" class="remain-time">
                {{ $t('remainTip') }}{{ item.remainTime }}
              </div>
            </li>
          </ul>
          <div @click="onCancel" class="stake-list-close">
            <img src="@/assets/images/close_w.png" />
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>
<style lang="scss" scoped>
.stake-list-modal-container {
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 999;
  --radius: 15px;
  --padding: 23px 15px 60px;
  --close-top: 24px;
  --close-right: 12px;
  --close-width: 22px;
  --title-font-size: 17px;
  --title-bottom: 10px;
  --list-top: 22px;
  --list-item-radius: 8px;
  --list-item-top: 10px;
  --list-item-height: 64px;
  --list-item-font-size: 15px;
  --list-item-font-color: #fff;
  --list-item-stop-font-color: rgba(255, 255, 255, 0.3);

  --stake-special-tip-ml: 5px;
  --stake-special-pd: 2px 6px;
  --stake-special-gap: 2px;
  --stake-special-br: 10px 10px 10px 2px;
  --stake-special-fs: 11px;
  --stake-special-top: -4px;
}
.stake-list-box {
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: var(--padding);
  background-color: var(--modal-bg);
  border-radius: var(--radius) var(--radius) 0 0;
}

.stake-list-title {
  padding-bottom: var(--title-bottom);
  font-size: var(--title-font-size);
  font-weight: 600;
  text-align: center;
  border-bottom: 1px solid #474747;
}

.stake-list-content {
  padding-top: var(--list-top);
}
.stake-list-content li {
  border: 1px solid #4b515c;
  border-radius: var(--list-item-radius);
  height: var(--list-item-height);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 3px;
  cursor: pointer;
  font-size: var(--list-item-font-size);
  color: var(--list-item-font-color);
}
.stake-list-content li .remain-time {
  font-size: 11px;
  color: #13e6bc;
  line-height: 15px;
  margin-bottom: -3px;
}
.stake-list-content li .special-tip {
  white-space: nowrap;
  margin-left: var(--stake-special-tip-ml);
  padding: var(--stake-special-pd);
  display: flex;
  align-items: center;
  gap: var(--stake-special-gap);
  background: linear-gradient(270deg, #b1d106 0%, #e4c60e 100%);
  border-radius: var(--stake-special-br);
  font-size: var(--stake-special-fs);
  position: absolute;
  top: var(--stake-special-top);
  left: 100%;
}
.stake-list-content li .lock-day {
  position: relative;
}
.stake-list-content li:not(:first-child) {
  margin-top: var(--list-item-top);
}
.stake-list-content li.active,
.stake-list-content li:hover {
  border-color: var(--primary-bg);
  background-color: #434a58;
}
.stake-list-content li.stop,
.stake-list-content li.stop .remain-time {
  color: var(--list-item-stop-font-color);
}
.stake-list-close {
  position: absolute;
  top: var(--close-top);
  right: var(--close-right);
  width: var(--close-width);
  cursor: pointer;
}
.stake-list-close img {
  width: 100%;
}

.stake-enter-active,
.stake-leave-active {
  transition: all 0.5s ease;
}

.stake-enter-from,
.stake-leave-to {
  opacity: 0;
  transform: translateY(100%);
}
</style>
