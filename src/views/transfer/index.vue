<template>
  <div class="transfer-page-container">
    <div class="transfer-page-header">
      <img class="icon-back" src="@/assets/images/user/icon_left_circular.png" @click="onBack" />
      <div class="title">{{ $t('transfer') }}</div>
    </div>
    <div class="transfer-container">
      <div class="transfer-top-container">
        <div class="transfer-top-container-l">
          <div class="l-dot l-dot-0"></div>
          <div class="l-dot"></div>
          <div class="l-dot"></div>
          <div class="l-dot"></div>
          <div class="l-dot l-dot-4"></div>
        </div>
        <div class="transfer-top-container-r">
          <div class="r-line">
            <label>{{ $t('From') }}</label>
            <span>{{ $t('Transferable Balance') }}</span>
          </div>
          <div class="r-line">
            <label>{{ $t('To') }}</label>
            <span>{{ $t('Restake Amount') }}</span>
          </div>
        </div>
      </div>
      <div class="transfer-title">{{ $t('Amount') }}</div>
      <div class="transfer-amount-box">
        <input type="text" :placeholder="$t('Please enter amount to transfer')" v-model="amount" />
        <div class="amount-line"></div>
        <div class="amount-btn" @click="onMax">{{ $t('Max') }}</div>
      </div>
      <div class="transfer-title">{{ $t('Available') }} {{ formatPrice(balance) }} FNXAI</div>
      <div class="btn-confirm cursor_p" @click="onSubmit" v-loading="sendLoading">
        {{ $t('transfer') }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { formatPrice, isLogin } from '@/utils'
import { accountTransfer, getMyAccount } from '@/api/user'
import message from '@/utils/message.js'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

const balance = ref('0')
const accountInfo = ref()
const amount = ref('')
const sendLoading = ref(false)
const accountType = route.query.accountType || '1'

const onBack = () => {
  router.back()
}

const onMax = () => {
  amount.value = formatPrice(balance.value)
}

const onTransfer1 = async () => {
  try {
    try {
      await accountTransfer(amount.value)
      message.success(t('Successful'))
      const res = await getMyAccount()
      balance.value = res.settleAmount
      accountInfo.value = res
      sendLoading.value = false
    } catch (error) {
      sendLoading.value = false
      return
    }
  } catch (error) {
    sendLoading.value = false
  }
}

const onSubmit = async () => {
  if (sendLoading.value) {
    return
  }
  if (isNaN(amount.value) || amount.value <= 0) {
    message.warning(t('Please enter amount to transfer'))
    return
  }
  if (balance.value <= 0 || amount.value > balance.value) {
    message.warning(t('Available Balance is not enough'))
    return
  }
  sendLoading.value = true
  if (accountType == '1') {
    onTransfer1()
  }
}

onMounted(async () => {
  if (isLogin()) {
    try {
      const res = await getMyAccount()
      balance.value = res.settleAmount
      accountInfo.value = res
    } catch (e) {
      //
    }
  }
})
</script>

<style lang="scss" scoped>
.transfer-page-container {
  padding-top: calc(var(--header-height) + 3px);
  min-height: calc(100vh - var(--footer-height));
  .transfer-page-header {
    padding: 11.5px 15px;
    display: flex;
    align-items: center;
    .title {
      width: 100%;
      font-size: 17px;
      color: #ffffff;
      line-height: 24px;
      text-align: center;
      padding-right: 35px;
    }
    .icon-back {
      width: 35px;
    }
  }
  .transfer-container {
    padding-left: 15px;
    padding-right: 15px;
    padding-top: 12px;
    padding-bottom: 38px;
    .transfer-top-container {
      padding-top: 3px;
      padding-left: 11.5px;
      padding-right: 12.5px;
      background-color: rgba(50, 56, 68, 0.5);
      border-radius: 7px;
      position: relative;
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;
      .transfer-top-container-l {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        .l-dot {
          width: 3px;
          height: 3px;
          border-radius: 50%;
          background-color: #2a5274;
        }
        .l-dot-0,
        .l-dot-4 {
          width: 5px;
          height: 5px;
        }
        .l-dot-0 {
          background-color: #e15167;
        }
        .l-dot-4 {
          background-color: #2bad6f;
        }
      }
      .transfer-top-container-r {
        flex: 1;
        .r-line {
          padding: 16px 0;
          display: flex;
          align-items: center;
          border-bottom: 1px solid #3e494d;
          &:last-child {
            border-bottom: none;
          }
          label {
            font-size: 14px;
            color: #607b92;
            line-height: 20px;
            flex-basis: 56px;
            text-align: left;
          }
          span {
            font-weight: 500;
            font-size: 14.5px;
            color: #ffffff;
            line-height: 20px;
          }
        }
      }
    }
    .transfer-title {
      padding-top: 14px;
      font-size: 13px;
      color: #ffffff;
      line-height: 20px;
    }
    .transfer-amount-box {
      margin-top: 15px;
      padding: 23px 12.5px;
      background-color: rgba(50, 56, 68, 0.71);
      border: 1px solid #5a6164;
      border-radius: 15.83px;
      display: flex;
      align-items: center;
      gap: 15px;
      input {
        flex: 1;
        font-weight: 500;
        font-size: 15px;
        line-height: 25px;
        color: #ffffff;
        &::placeholder {
          color: #cfd2d6;
        }
      }
      .amount-line {
        width: 1px;
        height: 17px;
        background-color: #e1e1e1;
      }
      .amount-btn {
        padding-right: 4.5px;
        font-weight: 600;
        font-size: 14px;
        color: #ffffff;
        line-height: 20px;
      }
    }
    .btn-confirm {
      width: 100%;
      height: 43px;
      background: #13e6bc;
      border-radius: 6px;
      font-size: 14px;
      color: #ffffff;
      line-height: 43px;
      margin-top: 23px;
      text-align: center;
    }
  }
}
</style>
