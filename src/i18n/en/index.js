export default {
  空白: '',
  Language: 'Language',
  MyPortfolio: 'My Portfolio',
  Logout: 'Logout',
  FollowUs: 'Follow Us',
  Empty: 'No Records～',
  'Connect Wallet': 'Connect Wallet',
  Stake: 'Stake',
  Staking: 'Staking',
  StakingTerms: 'Staking Terms',
  StakingTermsM: 'Staking<br/>Terms',
  DailyPayoutRate: 'Daily Payout Rate',
  DailyPayoutRateM: 'Daily<br/>Payout Rate',
  TotalStaked: 'Total Staked',
  TotalStakedM: 'Total<br/>Staked',
  Days: 'Days',
  Daily: 'Daily',
  copySuc: 'Copied',
  signMsg:
    'Please click "OK" to establish a connection to the Finanx AI decentralised application.',
  Return: 'Return',
  Agree: 'Agree',
  Reminder: 'Reminder',
  StakeSwitchTip: (ctx) =>
    `You are currently on ${ctx.named('chain')} network, are you sure to switch your current network to new network?`,
  SelectNetwork: 'Select a network',
  ETHEREUM: 'ETHEREUM',
  ARBITRUM: 'ARBITRUM',
  BNB: 'BNB',
  InvitationCode: 'Invitation Code',
  Enter: 'Enter',
  MinStakingAmount: 'Min. staking amount',
  Stakeon: 'Stake on',
  EnterStakeAmount: 'Enter stake amount',
  StakeTerms: 'Stake Terms',
  TotalPayout: 'Total Payout',
  IncludingPrincipal: 'Including principal',
  PrincipalReturnLong: 'Principal Return: Included in Daily Payout',
  PrincipalReturnLong2: 'Principal Return: Returned at maturity (including all interest)',
  Note: 'Note',
  Balance: 'Balance',
  Max: 'Max',
  Price: 'Price',
  EnterValidCode: 'Please enter valid invitation code',
  NoInviteTip: 'Are you sure you want to proceed with staking without entering an invitation code?',
  ContinueToStake: 'Continue to stake',
  StakeStatus: 'Status',
  StakingTime: 'Staking Time',
  StakeAmount: 'Stake Amount',
  PrincipalReturn: 'Principal Return',
  IncludedIn: ' Included in daily payout',
  IncludedIn2: 'Returned at maturity (including all interest)',
  StakingTermsMo: 'Staking Terms',
  TradingDays: 'Trading Days',
  stakeSuc: 'Stake Successful',
  Name: 'Name',
  Address: 'Address',
  'Invitation Code': 'Invitation Code',
  'My Stake': 'My Stake',
  'My List': 'My List',
  'Daily Bonus': 'Daily Bonus',
  'Bonus Wallet': 'Bonus Wallet',
  'S/N': 'S/N',
  'Staked Time': 'Staked Time',
  'Staked Amount (FNXAI)': 'Staked Amount (FNXAI)',
  'Daily Payout': 'Daily Payout',
  'Completed Days': 'Completed Days',
  'Next Disbursement Date': 'Next Disbursement Date',
  'Balance Payout': 'Balance Payout (FNXAI)',
  TablePaginationItems: (ctx) => `Page ${ctx.named('value')}`,
  TablePaginationTotal: (ctx) => `Total ${ctx.named('value')} pages`,
  'Total Community Staked': 'Total Community Staked',
  'Total Staked': 'Total Staked',
  'Referral Date': 'Referral Date',
  'Amount Staked(FNXAI)': 'Amount Staked (FNXAI)',
  'Amount Staked(USDT)': 'Amount Staked (USDT)',
  'Community Staked(FNXAI)': 'Community Staked (FNXAI)',
  'Community Staked(USDT)': 'Community Staked (USDT)',
  Network: 'Network',
  Status: 'Status',
  Send: 'Send',
  'Available Balance': 'Available Balance',
  Transactions: 'Transactions',
  More: 'More',
  'Current balance': 'Current Balance',
  'Closing balance': 'Closing balance',
  Received: 'Received',
  Withdraw: 'Withdraw',
  'Balance Bonus': 'Balance Bonus (FNXAI)',
  'Pending Balance': 'Pending Balance',
  'Current balance1': 'Current Balance',
  Completed: 'Completed',
  ALL: 'ALL',
  'On-going': 'On-going',
  redeemSuc: 'The record will appear after blockchain confirmation',
  'Pending processing': 'Pending processing',
  Successful: 'Successful',
  editSuc: 'Successful',
  'Profile Name Prompt': 'Profile Name Prompt',
  'Please enter your profile name': 'Please enter your profile name',
  'Enter your name': 'Enter your name',
  Done: 'Done',
  Details: 'Details',
  Transfer: 'Transfer',
  Confirm: 'Confirm',
  'Amount will ... blockchain network':
    'Amount will be transferred to your selected blockchain network',
  'Your Wallet Address': 'Your Wallet Address',
  'No records': 'No records',
  'Referral Bonus': 'Referral Bonus',
  'Cannot stake during the fundraising period': 'Cannot stake during the fundraising period',
  WithdrawDict: 'Withdraw',
  Search: 'Search',
  'Available Balance is not enough': 'Available Balance is not enough',
  'Received On': 'Received On',
  Hash: 'Hash',
  Wallet: 'Wallet',
  Result: 'Result',
  Remark: 'Remark',
  Remark1: 'Remark',
  'Staked Amount': 'Staked Amount',
  dailyInterestRate: 'Daily Bonus (FNXAI)',
  dailyInterestAmount: 'Daily Payout (FNXAI)',
  'Currently not available': 'Currently not available',
  'Withdraw On': 'Withdraw On',
  'Shareholder Bonus': 'Shareholder Bonus',
  'Total Asset': 'Total Asset',
  In: 'In',
  Out: 'Out',
  Home: 'Home',
  WithdrawRemark:
    'Remark: Only the current balance is available for transfer, while the pending balance is still being processed.',
  'Enter Wallet': 'Enter Wallet',
  Unclaimed: 'Balance',
  from: 'from',
  'Please fill in details below': 'Please fill in details below',
  'E-mail': 'E-mail',
  'Phone number': 'Phone number',
  Region: 'Region',
  'Please enter your E-mail': 'Please enter your E-mail',
  'Please enter your phone number': 'Please enter your phone number',
  'Please enter your region': 'Please enter your region',
  special: 'Special',
  remainTip: 'Remaining staking entries: ',
  'Extra Bonus': 'Enhanced Bonus',
  stakingReward: 'Staking Rewards (FNXAI)',
  Reinvest: 'Restake',
  type: 'Type',
  transfer: 'Transfer',
  'Pending Settlement': 'Pending Settlement',
  'Transferable Balance': 'Transferable Balance',
  'Restake Amount': 'Restake Amount',
  Restake: 'Restake',
  'Restake Time': 'Restake Time',
  From: 'From',
  To: 'To',
  'Please enter amount to transfer': 'Please enter amount to transfer',
  Available: 'Available',
  'Transfer Time': 'Transfer Time',
  Amount: 'Amount',
  'Shares Wallet': 'Shares',
  'Load more': 'Load more',

  'Status1': 'Status',
  'Payout Received': 'Payout Received',
  'Payout Balance': 'Payout Balance',
  'Dividends': 'Dividends',
  'Shares': 'Shares',
  'Deduction': 'Deduction',
  'Withdraw Amount': 'Withdraw Amount',
  'Not reviewed': 'Not reviewed',
  'Approved': 'Approved',
  'Issued': 'Issued',
  'Rejected after review': 'Rejected after review',
  'Pending Distribution': 'Pending Distribution',
  'New': 'New',
  'Select': 'Select',
  'No.': 'No.',
  'Date': 'Date',
  'Serial Number': 'Serial Number',
  'Dividends Amount（FNXAI）': 'Dividends Amount (FNXAI)',
  'Percentage': 'Percentage',
  'FNXAI : Shares Ratio': 'FNXAI : Shares Ratio',
  'Entitled Shares': 'Entitled Shares',
  'Distribution Date': 'Distribution Date',
  'Distributed': 'Distributed',
  PayoutRate: 'Payout Rate',
  PayoutRateM: 'Payout<br/>Rate',
  'Total Shares': 'Total Shares',
}
