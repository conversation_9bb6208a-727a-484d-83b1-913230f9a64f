import { createI18n } from 'vue-i18n'
import enLocale from './en'
import zhLocale from './zh'
import koLocale from './ko'
import { getUrlParams } from '@/utils'

const messages = {
  'en-US': enLocale,
  'zh-CN': zhLocale,
  'ko-KR': koLocale,
}

function saveLocaleToLocalStorage(val) {
  localStorage.setItem('lang', val)
}

function getSimilarLocale(val) {
  if (typeof val !== 'string') {
    return
  }

  // 校验语言格式
  const localeSplitArr = val.split('-')
  const lang = localeSplitArr[0]

  const country = localeSplitArr[localeSplitArr.length - 1]
  // lang 格式 e.g. en, zh
  if (!country) {
    // 根据 lang 找近似语言
    for (const key in messages) {
      const [itemLang] = key.split('-')
      if (itemLang.toLowerCase() === lang.toLowerCase()) {
        return key // 推断不了字面量, 只能断言
      }
    }
    return
  }
  // lang-country 格式, e.g. en-US, zh-CN
  for (const key in messages) {
    if (key.toLowerCase() === val.toLowerCase()) {
      return key // 推断不了字面量, 只能断言
    }
  }
}

/* 获取默认语言：优先从缓存读，其次从浏览器偏好设置读 */
export function getDefaultLocale() {
  const urlParams = getUrlParams(location.href)
  if (urlParams.lang) {
    let res = getSimilarLocale(urlParams.lang) || 'en-US'
    saveLocaleToLocalStorage(res)
    return res
  }
  // 从缓存读
  const cacheLocale = getSimilarLocale(localStorage.getItem('lang'))
  if (cacheLocale) {
    return cacheLocale
  }

  let result = 'en-US'
  // 从浏览器偏好设置读
  const browserLocale = getSimilarLocale(navigator.language)
  if (browserLocale) {
    result = browserLocale
  }
  // 保存到缓存
  saveLocaleToLocalStorage(result)
  return result
}
const defaultLocale = getDefaultLocale()

const i18n = createI18n({
  messages,
  locale: defaultLocale, // 默认使用的语言
  fallbackLocale: defaultLocale, // 未找到对应语言时使用的语言
  legacy: false, // 启用传统模式，这意味着插件将使用旧版的 Vue 2 API。
  globalInjection: true, // 是否将 $i18n 注入到全局 Vue 实例中
  fallbackRoot: true // 如果未找到翻译，是否回退到根语言
})

/* 切换语言 */
export function toggleLanguage(val) {
  if (val === i18n.global.locale) {
    return
  }
  saveLocaleToLocalStorage(val)
  i18n.global.locale = val
  location.href = location.pathname
}

export const languagesList = [
  {
    key: 'en-US',
    value: 'English'
  },
  {
    key: 'zh-CN',
    value: '简体中文'
  },
  {
    key: 'ko-KR',
    value: '한국어'
  }
]

export const languages = {
  'en-US': 'English',
  'zh-CN': '简体中文',
  'ko-KR': '한국어',
}

export const languagesFetch = {
  'en-US': 'en_US',
  'zh-CN': 'zh_CN',
  'ko-KR': 'ko_KR',
}

// 路由
export const languagesRoute = {
  'en-US': 'en',
  'zh-CN': 'zh',
  'ko-KR': 'ko',
}

export default i18n
