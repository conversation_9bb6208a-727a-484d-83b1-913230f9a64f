<script setup>
import ClipboardJS from 'clipboard'
import { RouterView } from 'vue-router'
import Header from '@/components/Header.vue'
import Footer from '@/components/Footer.vue'
import { useI18n } from 'vue-i18n'
import { onMounted } from 'vue'
import message from './utils/message'
import { languagesRoute } from '@/i18n/index.js'
import './utils/web3-setup'

const { locale, t } = useI18n()
onMounted(() => {
  document.querySelector('#app').className = languagesRoute[locale.value] ?? 'en'
  new ClipboardJS('.copy_icon').on('success', () => {
    message.success(t('common.copySuc'))
  })
})
</script>

<template>
    <Header />
    <RouterView />
    <Footer />
</template>

<style scoped></style>
