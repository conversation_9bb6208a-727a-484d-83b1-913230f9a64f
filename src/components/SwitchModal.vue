<script setup>
import { CHAINS, CHAINS_CONFIG } from '@/config'
import { getAccount, switchChain, watchAccount } from '@wagmi/core'
import { ref, watch, onUnmounted } from 'vue'

const props = defineProps({
  visible: <PERSON><PERSON><PERSON>
})

const emit = defineEmits(['cancel'])
const chains = CHAINS_CONFIG.chains

const onCancel = () => {
  emit('cancel')
}
const currentChainId = ref()
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      const { chainId } = getAccount(CHAINS_CONFIG)
      const chain = chains.find(chain => chain.id === chainId)
      currentChainId.value = chain && chain.id
    }
  },
  {
    immediate: true
  }
)


const unwatchAccount = watchAccount(CHAINS_CONFIG, {
  onChange(network) {
    const chain = chains.find(chain => chain.id === network.chainId)
    if (chain) {
      currentChainId.value = chain.id
    }
  }
})

onUnmounted(() => {
  unwatchAccount()
})

const loading = ref(false)
const choseNetwork = async (chainId) => {
  if (chainId == currentChainId.value) {
    return
  }
  loading.value = true
  try {
    await switchChain(CHAINS_CONFIG, {
      chainId
    })
  } catch (error) {
    //
  }
  loading.value = false
  emit('cancel')
}
</script>

<template>
  <Teleport to="body">
    <Transition name="switch">
      <div v-if="visible" class="switch-modal-container">
        <div v-loading="loading" class="switch-box">
          <div class="switch-title">{{ $t('SelectNetwork') }}</div>
          <div class="switch-content">
            <div
              @click="choseNetwork(CHAINS[0].id)"
              class="switch-item"
              :class="{ active: currentChainId == CHAINS[0].id }"
            >
              <img src="@/assets/images/eth_s.png" /><span>{{ $t('ETHEREUM') }}</span>
            </div>
            <div
              @click="choseNetwork(CHAINS[1].id)"
              class="switch-item"
              :class="{ active: currentChainId == CHAINS[1].id }"
            >
              <img src="@/assets/images/arbitrum_s.png" /><span>{{ $t('ARBITRUM') }}</span>
            </div>
            <div
              @click="choseNetwork(CHAINS[2].id)"
              class="switch-item"
              :class="{ active: currentChainId == CHAINS[2].id }"
            >
              <img src="@/assets/images/bnb_s.png" /><span>{{ $t('BNB') }}</span>
            </div>
          </div>
          <div @click="onCancel" class="switch-close">
            <img src="@/assets/images/close_w.png" />
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>
<style scoped>
.switch-modal-container {
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  --width: 375px;
  --radius: 15px;
  --padding: 22px 15px 25px;
  --close-top: 22px;
  --close-right: 12px;
  --close-width: 22px;
  --title-bottom: 16px;
  --title-font-size: 17px;
  --item-top: 18px;
  --item-height: 64px;
  --item-radius: 8px;
  --item-padding: 0 15px;
  --item-font-size: 15px;
  --item-icon-width: 37px;
  --item-gap: 18px;
}

.switch-box {
  width: var(--width);
  border-radius: var(--radius);
  background-color: var(--modal-bg);
  padding: var(--padding);
  position: relative;
  overflow: hidden;
}

.switch-item {
  height: var(--item-height);
  margin-top: var(--item-top);
  border-radius: var(--item-radius);
  padding: var(--item-padding);
  display: flex;
  align-items: center;
  gap: var(--item-gap);
  border: 1px solid #4b515c;
  cursor: pointer;
}

.switch-item.active,
.switch-item:hover {
  border-color: var(--primary-bg);
}

.switch-item img {
  width: var(--item-icon-width);
}

.switch-item span {
  font-size: var(--item-font-size);
}

.switch-close {
  position: absolute;
  top: var(--close-top);
  right: var(--close-right);
  width: var(--close-width);
  cursor: pointer;
}

.switch-close img {
  width: var(--close-width);
}

.switch-title {
  font-size: var(--title-font-size);
  font-weight: 600;
  text-align: center;
  padding-bottom: var(--title-bottom);
  border-bottom: 1px solid #474747;
}

.switch-enter-active,
.switch-leave-active {
  transition: opacity 0.5s ease;
}

.switch-enter-from,
.switch-leave-to {
  opacity: 0;
}

@media (max-width: 767px) {
  .switch-modal-container {
    background-color: rgba(0, 0, 0, 0.6);
    align-items: flex-end;
    justify-content: center;
    --width: 100%;
    --radius: 15px;
    --padding: 23px 15px 40.5px;
    --close-top: 24px;
    --title-bottom: 15px;
    --item-top: 18.5px;
  }

  .switch-box {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }

  .switch-enter-active,
  .switch-leave-active {
    transition: all 0.5s ease;
  }

  .switch-enter-from,
  .switch-leave-to {
    opacity: 0;
    transform: translateY(100%);
  }
}
</style>
