<script setup>
import { computed, ref, watch, onMounted } from 'vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import en from 'element-plus/es/locale/lang/en'
import { useI18n } from 'vue-i18n'

const propsData = defineProps({
  columns: Array,
  searchParams: Object,
  getPageList: Function,
  tableData: Array,
  noMargin: Boolean,
  modal: Boolean,
  openSingleChose: Boolean,
  showSummary: Boolean,
  summaryMethod: Function,
  stripe: Boolean,
  cellClick: Function,
  noInit: Boolean,
  showPointer: Function,
  columnsClassName: String
})

const emits = defineEmits(['onChangeSize', 'onCurrentChange', 'cellClick'])
const handleTableCurrentChange = (record) => {
  emits('onCurrentChange', record)
}
const onCellClick = (row, column, cell, event) => {
  emits('cellClick', row, column, cell, event)
}

const { locale } = useI18n()

const pLocale = computed(() => {
  return locale.value === 'zh-CN' ? zhCn : en
})

const columns = ref(propsData.columns)
const data = ref([])
const loading = ref(false)
const pageSize = ref(50)
const pageNum = ref(1)
const total = ref(1)
const pages = ref(1)
const currentNum = ref(1)

watch(
  () => propsData.columns,
  () => {
    columns.value = propsData.columns
  }
)

const getPageList = async () => {
  try {
    loading.value = true
    const params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      ...(propsData.searchParams || {})
    }
    const res = await propsData.getPageList(params)
    total.value = res.total
    pages.value = res.pages
    data.value = res.list
    currentNum.value = res.pageNum
  } catch (error) {
    //
  }
  loading.value = false
}

const handleSearch = () => {
  setTimeout(() => {
    getPageList()
  }, 0)
}

const hanldeReset = () => {
  pageNum.value = 1
  setTimeout(() => {
    getPageList()
  }, 0)
}

defineExpose({ handleSearch, hanldeReset })

onMounted(() => {
  if (propsData.modal) {
    pageSize.value = 50
  }
  if (!propsData.noInit) {
    getPageList()
  }
})

const handleCurrentChange = (current) => {
  pageNum.value = current
  getPageList()
}
// @size
const handleSizeChange = () => {
  getPageList()
}

const layout = computed(() => {
  if (propsData.modal) {
    return 'prev, pager, next'
  }
  return 'prev, pager, next'
})
</script>

<template>
  <div
    :class="{
      tab_box: true,
      modal_tab_box: modal
    }"
  >
    <el-table
      :data="tableData ? tableData : data"
      class="magif_table"
      :highlight-current-row="!!openSingleChose"
      @current-change="handleTableCurrentChange"
      border
      v-loading="loading"
      :show-summary="showSummary"
      :summary-method="summaryMethod"
      :stripe="stripe"
      @cell-click="onCellClick"
    >
      <el-table-column
        v-for="(item, index) in columns"
        :key="index"
        :label="item.title ? item.title : $t(item.titleKey)"
        :width="item.width"
        :fixed="item.fixed"
        :prop="item.dataIndex"
      >
        <template #default="scope">
          <slot
            :name="item.dataIndex"
            :value="scope.row[item.dataIndex]"
            :data="scope.row"
            :index="index"
            :sIndex="scope.$index"
          >
            <div
              :class="{
                cursor_p: showPointer ? showPointer(scope.row) : false,
                [columnsClassName]: columnsClassName
              }"
            >
              {{
                (item.render
                  ? item.render(scope.row[item.dataIndex], scope.row)
                  : scope.row[item.dataIndex]) ?? '-'
              }}
            </div>
          </slot>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div class="table_pagination">
    <div class="table_pagination_total">
      {{ $t('TablePaginationItems', { value: `${pageNum}` }) }}
      /
      {{ $t('TablePaginationTotal', { value: pages }) }}
    </div>
    <el-config-provider :locale="pLocale">
      <el-pagination
        :total="total"
        v-model:page-size="pageSize"
        :layout="layout"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        :pager-count="5"
      />
    </el-config-provider>
  </div>
</template>
