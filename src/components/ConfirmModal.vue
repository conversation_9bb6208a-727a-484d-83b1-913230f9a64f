<script setup>
defineProps({
  title: String,
  content: String,
  cancelText: String,
  okText: String,
  visible: Boolean,
  largeBtn: Boolean
})

const emit = defineEmits(['cancel', 'confirm'])

const onCancel = () => {
  emit('cancel')
}
const onConfirm = () => {
  emit('confirm')
}
</script>

<template>
  <Teleport to="body">
    <Transition name="confirm">
      <div v-if="visible" class="confirm-container">
        <div class="confirm-box">
          <div class="confirm-title">{{ title || $t('Reminder') }}</div>
          <div class="confirm-content">{{ content }}</div>
          <div class="confirm-actions">
            <div @click="onCancel" class="confirm-btn">{{ cancelText || $t('Return') }}</div>
            <div
              @click="onConfirm"
              class="confirm-btn confirm-btn-primary"
              :class="{
                'confirm-btn-large': largeBtn
              }"
            >
              {{ okText || $t('Agree') }}
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>
<style scoped>
.confirm-container {
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  --width: 300px;
  --radius: 15px;
  --padding: 26px 24px 28px;
  --title-font-size: 17px;
  --content-top: 17px;
  --content-font-size: 14px;
  --action-top: 36px;
  --action-gap: 9px;
  --btn-radius: 6px;
  --btn-height: 43px;
  --btn-font-size: 15px;
}
.confirm-container .confirm-box {
  width: var(--width);
  border-radius: var(--radius);
  background-color: var(--modal-bg);
  padding: var(--padding);
}
.confirm-title {
  font-size: var(--title-font-size);
  font-weight: 600;
  text-align: center;
}
.confirm-content {
  padding-top: var(--content-top);
  font-size: var(--content-font-size);
  color: #d2d6df;
}
.confirm-actions {
  display: flex;
  align-items: center;
  gap: var(--action-gap);
  margin-top: var(--action-top);
}
.confirm-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: var(--btn-height);
  border-radius: var(--btn-radius);
  font-size: var(--btn-font-size);
  color: #fff;
  font-weight: 600;
  border: 1px solid #737984;
  cursor: pointer;
}
.confirm-btn:hover {
  opacity: 0.92;
}
.confirm-btn.confirm-btn-primary {
  border: none;
  background-color: var(--primary-bg);
}
.confirm-btn.confirm-btn-primary.confirm-btn-large {
  flex: 2;
}

.confirm-enter-active,
.confirm-leave-active {
  transition: opacity 0.5s ease;
}

.confirm-enter-from,
.confirm-leave-to {
  opacity: 0;
}

@media (max-width: 767px) {
  .confirm-container {
    --width: 79.7%;
    --padding: 25.5px 24px 28px;
    --content-top: 18.5px;
    --content-font-size: 13px;
    --action-top: 35.5px;
    --action-gap: 8.5px;
    --btn-font-size: 14px;
  }
}
</style>
