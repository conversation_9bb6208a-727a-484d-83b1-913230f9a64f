<script setup>
import { useI18n } from 'vue-i18n'

defineProps({})

const { locale } = useI18n()
</script>

<template>
  <div class="footer-container">
    <div class="footer-left">
      <div class="follow-us">{{ $t('FollowUs') }}</div>
      <div class="copyright">© 2024 Finanx AI</div>
    </div>
    <div class="footer-split"></div>
    <ul class="footer-links">
      <li>
        <a href="https://www.facebook.com/profile.php?id=61566965561194" target="_blank"
          ><img src="@/assets/images/facebook.png"
        /></a>
      </li>
      <li>
        <a href="https://www.instagram.com/finanxai" target="_blank"
          ><img src="@/assets/images/Ins.png"
        /></a>
      </li>
      <li>
        <a href="https://discord.com/invite/finanxai" target="_blank"
          ><img src="@/assets/images/Discord.png"
        /></a>
      </li>
      <li>
        <a href="https://linktr.ee/finanxai" target="_blank"
          ><img src="@/assets/images/linktree.png"
        /></a>
      </li>
      <li>
        <a href="https://www.linkedin.com/company/finanxai" target="_blank"
          ><img src="@/assets/images/linkedin.png"
        /></a>
      </li>
      <li>
        <a href="https://x.com/Finanx_AI" target="_blank"
          ><img src="@/assets/images/twitter.png"
        /></a>
      </li>
      <li>
        <a href="https://t.me/finanxai" target="_blank"
          ><img src="@/assets/images/telegram.png"
        /></a>
      </li>
      <li>
        <a v-if="locale == 'zh-CN'" href="https://finanxaicn.medium.com" target="_blank"
          ><img src="@/assets/images/medium.png"
        /></a>
        <a v-else href="https://fnxai.medium.com" target="_blank"
          ><img src="@/assets/images/medium.png"
        /></a>
      </li>
      <li>
        <a v-if="locale == 'ko-KR'" href="https://fnxai.gitbook.io/wp/finanx-ai" target="_blank"
          ><img src="@/assets/images/white_paper.png"
        /></a>
        <a v-else-if="locale == 'zh-CN'" href="https://fnxai.gitbook.io/cn" target="_blank"
          ><img src="@/assets/images/white_paper.png"
        /></a>
        <a v-else href="https://fnxai.gitbook.io/wp" target="_blank"
          ><img src="@/assets/images/white_paper.png"
        /></a>
      </li>
    </ul>
    <div class="copyright-m">© 2024 Finanx AI</div>
  </div>
</template>

<style scoped>
.footer-container {
  background-color: var(--footer-background);
  height: var(--footer-height);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  --followus-font-size: 16px;
  --copyright-font-size: 12px;
  --copyright-top: 7px;
  --split-margin: 0 50px 0 91px;
  --split-height: 42px;
  --split-color: rgba(255, 255, 255, 0.2);
  --link-gap: 31px;
  --link-width: 35px;
}
.footer-left .follow-us {
  font-size: var(--followus-font-size);
  font-weight: 500;
}
.footer-left .copyright {
  font-size: var(--copyright-font-size);
  color: var(--color-tertiary-text);
  margin-top: var(--copyright-top);
}
.footer-split {
  margin: var(--split-margin);
  width: 1px;
  height: var(--split-height);
  background-color: var(--split-color);
}
.footer-links {
  display: flex;
  align-items: center;
  gap: var(--link-gap);
  font-size: 0;
}
.footer-links img {
  width: var(--link-width);
}
.copyright-m {
  display: none;
}
@media (min-width: 1536px) {
  .footer-container {
    --followus-font-size: 17px;
    --copyright-font-size: 13px;
    --split-margin: 0 60px 0 101px;
    --split-height: 46px;
    --split-color: rgba(255, 255, 255, 0.2);
    --link-gap: 33px;
    --link-width: 38px;
  }
}
@media (max-width: 1024px) {
  .footer-container {
    flex-wrap: wrap;
    padding: 8px 0;
    --followus-font-size: 15px;
    --copyright-m-font-size: 11px;
    --link-gap: 28px;
    --link-width: 28px;
  }
  .footer-left {
    display: flex;
  }
  .footer-left .follow-us {
    color: var(--color-tertiary-text);
  }
  .footer-left,
  .footer-links {
    width: 100%;
    flex-shrink: 0;
    justify-content: center;
  }
  .footer-left .copyright {
    display: none;
  }
  .footer-split {
    display: none;
  }
  .copyright-m {
    display: block;
    font-size: var(--copyright-m-font-size);
    color: var(--color-tertiary-text);
  }
}
@media (max-width: 767px) {
  .footer-container {
    padding: 6px 0;
    --followus-font-size: 13px;
    --copyright-m-font-size: 9px;
    --link-gap: 15px;
    --link-width: 24px;
    --footer-height: 140px;
  }
  .footer-links {
    padding: 15px 15px;
    flex-wrap: wrap;
  }
}
</style>
