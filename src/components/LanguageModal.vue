<script setup>
import { useI18n } from 'vue-i18n'
import { toggleLanguage, languagesList } from '@/i18n/index.js'

const { locale } = useI18n()

const props = defineProps({
  visible: Boolean
})

const emit = defineEmits(['cancel'])

const onCancel = () => {
  emit('cancel')
}

const choseLanguage = (command) => {
  if (command == locale.value) {
    return
  }
  toggleLanguage(command)
  emit('cancel')
}
</script>

<template>
  <Teleport to="body">
    <Transition name="language">
      <div v-if="props.visible" class="language-modal-container">
        <div class="language-box">
          <div class="language-title">{{ $t('Language') }}</div>
          <div class="language-content">
            <div
              v-for="item in languagesList" :key="item.key"
              @click="choseLanguage(item.key)"
              class="language-item"
              :class="{ active: locale == item.key }"
            >
              <span>{{ item.value }}</span>
            </div>
          </div>
          <div @click="onCancel" class="language-close">
            <img src="@/assets/images/close_w.png" />
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>
<style scoped>
.language-modal-container {
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  --width: 375px;
  --radius: 15px;
  --padding: 22px 15px 25px;
  --close-top: 22px;
  --close-right: 12px;
  --close-width: 22px;
  --title-bottom: 16px;
  --title-font-size: 17px;
  --item-top: 9px;
  --item-height: 52px;
  --item-radius: 8px;
  --item-padding: 0 15px;
  --item-font-size: 14px;
  --item-icon-width: 37px;
  --item-gap: 18px;
}
.language-box {
  width: var(--width);
  border-radius: var(--radius);
  background-color: var(--modal-bg);
  padding: var(--padding);
  position: relative;
  overflow: hidden;
}
.language-item {
  height: var(--item-height);
  margin-top: var(--item-top);
  border-radius: var(--item-radius);
  padding: var(--item-padding);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--item-gap);
  border: 1px solid #4b515c;
  cursor: pointer;
}
.language-item.active,
.language-item:hover {
  border-color: var(--primary-bg);
}
.language-item img {
  width: var(--item-icon-width);
}
.language-item span {
  font-size: var(--item-font-size);
}
.language-close {
  position: absolute;
  top: var(--close-top);
  right: var(--close-right);
  width: var(--close-width);
  cursor: pointer;
}
.language-close img {
  width: var(--close-width);
}
.language-title {
  font-size: var(--title-font-size);
  font-weight: 600;
  text-align: center;
  padding-bottom: var(--title-bottom);
  border-bottom: 1px solid #474747;
}

.language-enter-active,
.language-leave-active {
  transition: opacity 0.5s ease;
}

.language-enter-from,
.language-leave-to {
  opacity: 0;
}
@media (max-width: 767px) {
  .language-modal-container {
    background-color: rgba(0, 0, 0, 0.6);
    align-items: flex-end;
    justify-content: center;
    --width: 100%;
    --radius: 15px;
    --padding: 23px 15px 40.5px;
    --close-top: 24px;
    --title-bottom: 15px;
    --item-top: 13px;
  }
  .language-box {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
  .language-enter-active,
  .language-leave-active {
    transition: all 0.5s ease;
  }

  .language-enter-from,
  .language-leave-to {
    opacity: 0;
    transform: translateY(100%);
  }
}
</style>
