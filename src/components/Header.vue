<script setup>
import { computed, onMounted, onUnmounted, onBeforeMount, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { RouterLink, useRouter, useRoute } from 'vue-router'
import { languages } from '@/i18n'
import { getLoginNonce, registerAndloginIn } from '@/api/user'
import { clearUser, handleWeb3Error, isLogin, isMobileWeb, setUser, truncateString } from '@/utils'
import {
  getChainId,
  getAccount,
  signMessage,
  switchChain,
  watchAccount
  // disconnect,
} from '@wagmi/core'
import { useUserStore } from '@/stores/user'
import { CHAINS, CHAINS_CONFIG } from '@/config'
import SwitchModal from './SwitchModal.vue'
import message from '@/utils/message.js'
import ClipboardJS from 'clipboard'
import LanguageModal from '@/components/LanguageModal.vue'
import { createModal } from '@/utils/web3-setup.js'
import { useAppKitAccount, useDisconnect } from '@reown/appkit/vue'

defineProps({})

const router = useRouter()
const route = useRoute()

const { locale, t } = useI18n()
const userStore = useUserStore()
const modal = createModal

const useAccount = useAppKitAccount()

const chains = CHAINS_CONFIG.chains

const accountData = useAppKitAccount()
const { disconnect } = useDisconnect()

watch(
  () => [accountData.value.isConnected, accountData.value.status],
  ([nowIsConnected, nowStatus]) => {
    if (
      nowIsConnected &&
      (!accountData.value.address || accountData.value.address == 'undefined')
    ) {
      setTimeout(() => {
        disconnect()
      }, 100)
    } else if (!nowIsConnected && nowStatus == 'disconnected' && !accountData.value.address) {
      setTimeout(() => {
        disconnect()
      }, 100)
    }
  }
)

let logining = false
const doLogin = async (address) => {
  if (logining) return
  try {
    logining = true
    signAndLogin(address)
  } catch (error) {
    setTimeout(() => {
      logining = false
    }, 500)
  }
}

const signAndLogin = async (address) => {
  let signMsg = ''
  try {
    const { nonce } = await getLoginNonce(address)
    if (isMobileWeb()) {
      const flag = window.confirm(t('signMsg'))
      if (!flag) {
        return
      }
    }
    signMsg = await signMessage(CHAINS_CONFIG, {
      message: nonce
    })
  } catch (error) {
    handleWeb3Error(error)
    setTimeout(() => {
      logining = false
    }, 500)
    return
  }
  if (!signMsg) {
    return
  }
  const res = await registerAndloginIn({ address, sign: signMsg })
  setUser(res)
  await userStore.getUserMsg()
  // setTimeout(() => {
  //   location.reload()
  // }, 100)
  logining = false
}

const currentChainId = ref(1)
const prevAddress = ref()

// let isLoad = false

onMounted(() => {})

onBeforeMount(async () => {
  // console.log(isConnected, status, useAccount.value.isConnected, useAccount.value )
  // await disconnect(CHAINS_CONFIG);
})

const toLogin = async () => {
  let { isConnected } = getAccount(CHAINS_CONFIG)

  if (!isConnected && !useAccount.value.isConnected) {
    const chainId = getChainId(CHAINS_CONFIG)
    const chain = chains.find((chain) => chain.id === chainId)

    if (chainId && !chain) {
      try {
        await switchChain(CHAINS_CONFIG, {
          chainId: CHAINS[0].id
        })
      } catch (error) {
        console.log('switchChainError:', error)
        return
      }
    }
  }

  if (isConnected || useAccount.value.isConnected) {
    try {
      await disconnect()
    } catch (error) {
      console.log('isConnected:', error)
    }
  }

  modal.open()
}

const handleQuit = async () => {
  userStore.clearUser()
  clearUser(false)
  await disconnect()
  location.href = '/'
}

const userAddressTrue = computed(() => {
  return (userStore.user && userStore.user.address) || ''
})
const userAddress = computed(() => {
  return truncateString((userStore.user && userStore.user.address) || '')
})
const unwatchAccountInfo = ref([])
const unwatchAccount = watchAccount(CHAINS_CONFIG, {
  async onChange(account, prevAccount) {
    // console.log('unwatchAccount', account, prevAccount, userStore.user)
    unwatchAccountInfo.value.push({
      account
    })
    // 断开链接
    if (account.isDisconnected) {
      prevAddress.value = null
      userStore.clearUser()
      clearUser(false)
      await disconnect()
      if (prevAccount.isConnected) {
        router.replace('/')
      }
      return
    }
    const chain = chains.find((chain) => chain.id === account.chainId)

    if (account.chainId && !chain) {
      currentChainId.value = CHAINS[0].id
    } else {
      currentChainId.value = account.chainId
    }
    if (
      account.isConnected &&
      prevAddress.value == account.address &&
      userStore.user &&
      isLogin()
    ) {
      // 刷新页面
      // if(account.chainId && prevAccount.chainId && account.chainId != prevAccount.chainId && route.name != 'home') {
      //   location.reload()
      // }
      userStore.getUserMsg()
      return
    }
    prevAddress.value = account.address
    if (account.isConnected && account.address) {
      // 本地缓存的用户和钱包地址不同 或未登录
      if (
        !userStore.user ||
        !userStore.user.address ||
        account.address != ((userStore.user && userStore.user.address) || '') ||
        !isLogin()
      ) {
        userStore.clearUser()
        clearUser(false)
        doLogin(account.address)
        router.replace('/')
      } else {
        // 本地缓存的用户和钱包地址相同
        userStore.getUserMsg()
      }
      // setTimeout(() => {
      //   isLoad = true
      // }, 10)
    }
  }
})

onUnmounted(() => {
  unwatchAccount()
})

const showMenu = ref(false)
const onEnter = () => {
  showMenu.value = true
}
const onLeave = () => {
  showMenu.value = false
}

const goUserPage = () => {
  if (userAddress.value) {
    router.push('/user')
  } else {
    toLogin()
  }
  showMenu.value = false
}

const goHome = () => {
  router.push('/')
  showMenu.value = false
}

const switchVisible = ref(false)

const onSwitchCancel = () => {
  switchVisible.value = false
  // location.reload()
}

const languageVisible = ref(false)

const onLanguageCancel = () => {
  languageVisible.value = false
}

const showHome = ref(false)
watch(
  () => route.name,
  (newRouteName) => {
    showHome.value = !(!newRouteName || newRouteName === 'home')
  }
)

const onHandleCopy = (event, value) => {
  if (!value || value == '') {
    return
  }
  // 创建 ClipboardJS 实例并绑定到点击的元素
  const clipboard = new ClipboardJS(event.target, {
    text: () => value // 要复制的内容
  })

  // 监听复制成功事件
  clipboard.on('success', () => {
    message.success(t('copySuc'))
    // 复制成功后销毁 Clipboard 实例
    clipboard.destroy()
  })

  // 监听复制失败事件
  clipboard.on('error', () => {
    // alert('复制失败');
    // 复制失败后也销毁 Clipboard 实例
    clipboard.destroy()
  })

  // 手动触发点击事件，执行复制操作
  clipboard.onClick(event)
}
</script>

<template>
  <div class="header-container" style="flex-wrap: wrap">
    <div class="header-inner">
      <div class="header-left">
        <router-link to="/"><img class="header-logo" src="@/assets/images/logo.png" /></router-link>
      </div>
      <div class="header-right">
        <div v-if="!userAddress" @click="toLogin" class="header-wallet">
          <img class="wallet-icon" src="@/assets/images/wallet.png" /><span>{{
            $t('Connect Wallet')
          }}</span>
        </div>
        <div v-else class="header-wallet">
          <div @click="switchVisible = true" class="network-icon">
            <img
              v-if="currentChainId == CHAINS[0].id"
              class="wallet-icon"
              src="@/assets/images/eth_s.png"
            />
            <img
              v-if="currentChainId == CHAINS[1].id"
              class="wallet-icon"
              src="@/assets/images/arbitrum_s.png"
            />
            <img
              v-if="currentChainId == CHAINS[2].id"
              class="wallet-icon"
              src="@/assets/images/bnb_s.png"
            />
          </div>
          <span @click="goUserPage">{{ userAddress }}</span>
          <img
            class="icon-copy cursor_p"
            src="@/assets/images/copy_white.png"
            @click.stop="(e) => onHandleCopy(e, userAddressTrue)"
          />
        </div>
        <div class="menu-icon" @mouseenter="onEnter" @mouseleave="onLeave">
          <img v-if="!showMenu" src="@/assets/images/menu.png" /><img
            v-else
            src="@/assets/images/close_w.png"
          />
          <div v-if="showMenu" class="list">
            <dl>
              <dd @click="languageVisible = true">
                <img src="@/assets/images/icon_language.png" />{{ languages[locale] }}
              </dd>
              <!--              <dd v-if="locale !== 'zh-CN'"  @click="choseLanguage('zh-CN')">-->
              <!--                <img src="@/assets/images/zh.png" />简体中文-->
              <!--              </dd>-->
              <!--              <dd v-if="locale !== 'ko-KR'"  @click="choseLanguage('ko-KR')">-->
              <!--                <img src="@/assets/images/zh.png" />한국어-->
              <!--              </dd>-->
              <dd @click="goHome" v-if="showHome">
                <img src="@/assets/images/home.png" />{{ $t('Home') }}
              </dd>
              <dd @click="goUserPage" v-else>
                <img src="@/assets/images/profile.png" />{{ $t('MyPortfolio') }}
              </dd>
              <dd v-if="userAddress" @click="handleQuit">
                <img src="@/assets/images/quit.png" />{{ $t('Logout') }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
    <!--    <div style="color: white; width: 100%; height: 50px; overflow: scroll; background-color: #020E24; padding: 10px 20px; word-break: break-all;">
          <div>useAccount-isConnected: {{useAccount.isConnected}} {{useAccount.status}}</div>
          <div v-for="(item, index) in unwatchAccountInfo" :key="index">
            <div>status: {{item.account?.status}}</div>
            <div>address: {{item.account?.address}}</div>
            <div>address: {{item.account?.address}}</div>
            <div>chainId: {{item.account?.chainId}}</div>
            <div>status: {{item.account?.status}}</div>
            <div>isConnected: {{item.account?.isConnected}}</div>
            <div>isConnecting: {{item.account?.isConnecting}}</div>
            <div>isDisconnected: {{item.account?.isDisconnected}}</div>
            <div>isReconnecting: {{item.account?.isReconnecting}}</div>
        <div>-------------------------------</div>
      </div>
    </div>-->

    <switch-modal :visible="switchVisible" @cancel="onSwitchCancel" />
    <language-modal :visible="languageVisible" @cancel="onLanguageCancel" />
  </div>
</template>

<style scoped>
.header-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: var(--header-background);
  display: flex;
  align-items: center;
  height: var(--header-height);
  --logo-height: 34px;
  --wallet-height: 42px;
  --wallet-radius: 21px;
  --wallet-padding: 0 14px;
  --wallet-icon-width: 22px;
  --wallet-text-left: 6px;
  --wallet-font-size: 15px;
  --menu-icon-left: 10px;
  --menu-icon-width: 24px;

  --lang-list-ptop: 30px;
  --lang-list-inner-padding: 6px 18px;
  --lang-list-inner-width: 168px;
  --lang-border-radius: 16px;
  --lang-content-font-size: 15px;
  --lang-item-padding: 14px 0;
  --lang-item-icon-width: 22px;
  --lang-item-icon-right: 9px;
}
.header-inner {
  width: var(--header-width);
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.header-logo {
  height: var(--logo-height);
}
.header-right {
  position: relative;
  display: flex;
  align-items: center;
}
.header-wallet {
  height: var(--wallet-height);
  border-radius: var(--wallet-radius);
  display: flex;
  align-items: center;
  padding: var(--wallet-padding);
  background-color: rgba(255, 255, 255, 0.1);
  cursor: pointer;
}
.header-wallet .icon-copy {
  width: 17px;
  margin-left: 5px;
}
.header-wallet .network-icon {
  border-radius: var(--wallet-icon-width);
  overflow: hidden;
  font-size: 0;
}
.header-wallet .wallet-icon {
  width: var(--wallet-icon-width);
}
.header-wallet span {
  padding-left: var(--wallet-text-left);
  font-size: var(--wallet-font-size);
  font-weight: 600;
}
.header-right .menu-icon {
  margin-left: var(--menu-icon-left);
  width: var(--menu-icon-width);
  font-size: 0;
  cursor: pointer;
}
.menu-icon > img {
  width: 100%;
}
.header-right .list {
  position: absolute;
  z-index: 999;
  right: 0;
  top: var(--menu-icon-width);
  padding-top: var(--lang-list-ptop);
}
.header-right dl {
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.75);
  background-color: #353c48;
  border-radius: var(--lang-border-radius);
  padding: var(--lang-list-inner-padding);
  width: var(--lang-list-inner-width);
  font-size: 0;
}
dl dd {
  display: flex;
  align-items: center;
  font-size: var(--lang-content-font-size);
  font-weight: 600;
  padding: var(--lang-item-padding);
  cursor: pointer;
}
dl dd:not(:last-child) {
  border-bottom: 1px solid #575b62;
}
dd img {
  width: var(--lang-item-icon-width);
  margin-right: var(--lang-item-icon-right);
}

@media (min-width: 1536px) {
  .header-container {
    --logo-height: 40px;
    --wallet-height: 46px;
    --wallet-radius: 23px;
    --wallet-padding: 0 16px;
    --wallet-icon-width: 24px;
    --wallet-text-left: 8px;
    --wallet-font-size: 17px;
    --menu-icon-left: 12px;
    --menu-icon-width: 26px;

    --lang-list-inner-padding: 7px 19px;
    --lang-list-inner-width: 178px;
    --lang-border-radius: 20px;
    --lang-content-font-size: 16px;
    --lang-item-padding: 10px 0;
    --lang-item-icon-width: 24px;
    --lang-item-icon-right: 11px;
  }
}
@media (max-width: 1024px) {
  .header-container {
    --logo-height: 30px;
    --wallet-height: 38px;
    --wallet-radius: 19px;
    --wallet-padding: 0 12px 0 15px;
    --wallet-icon-width: 20px;
    --wallet-text-left: 7px;
    --wallet-font-size: 14px;
    --menu-icon-left: 8px;
    --menu-icon-width: 23px;

    --lang-list-ptop: 24px;
  }

  .header-inner {
    padding: 0 30px;
  }
  .header-right {
    margin-left: 30px;
    font-size: 0;
  }
}
@media (max-width: 767px) {
  .header-container {
    --logo-height: 25px;
    --wallet-height: 35px;
    --wallet-radius: 17.5px;
    --wallet-padding: 0 10.5px 0 13.5px;
    --wallet-icon-width: 18px;
    --wallet-text-left: 6px;
    --wallet-font-size: 13px;
    --menu-icon-left: 6px;
    --menu-icon-width: 22px;

    --lang-list-ptop: 20px;
    --lang-list-inner-padding: 6.5px 23px;
    --lang-border-radius: 13.5px;
    --lang-content-font-size: 14px;
    --lang-item-padding: 17px 0;
  }

  .header-inner {
    padding: 0 15px;
  }
}
</style>
