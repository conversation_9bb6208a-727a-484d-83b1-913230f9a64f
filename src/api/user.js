import fetch from '../utils/fetch'

export function getUserInfo() {
  return fetch('/core/v1/cuser/my')
}

// 获取nonce
export async function getLoginNonce(address) {
  return fetch('/core/v1/cuser/public/nonce', {
    address
  })
}

// 登录
export function registerAndloginIn(params) {
  return fetch('/core/v1/cuser/public/registerAndloginIn', params)
}

// 修改个人资料
export function editUserProfile(param) {
  return fetch('/core/v1/user/edit_profile', param)
}
// 验证邀请码
export function checkInviteCode(param) {
  return fetch('/core/v1/user/check_invite_code', param)
}

// 分页获取奖励审核
export function getPageRewards(param) {
  return fetch('/core/v1/reward/page_front', param)
}

// 分页条件查询标的订单/理财账户
export function getPageProductOrder(param) {
  return fetch('/core/v1/product_order/page_front', param)
}

// 质押总金额
export function getProductOrderTotalAmount(param) {
  return fetch('/core/v1/product_order/order_total_amount', param)
}

// 可提币总量
export function getProductOrderSumAmount(param) {
  return fetch('/core/v1/product_order/sum_amount', param)
}

// 分页条件查询理财账户流水
export function getPageProductOrderIncome(param) {
  return fetch('/core/v1/product_order_income/page_front', {
    pageNum: 1,
    pageSize: 5,
    ...param
  })
}

// 分页条件查询理财账户流水（按月）
export function getPageProductOrderIcomeByMonth(param) {
  return fetch('/core/v1/product_order_income/page_front_by_month', {
    pageNum: 1,
    pageSize: 50,
    ...param
  })
}

// 发放明细
export function getPageProductOrderIcomeReleaseDetails(param) {
  return fetch('/core/v1/product_order_income/release_details_list', param)
}

// 详情查询理财账户流水
export function getProductOrderIncomeDetail(id) {
  return fetch('/core/v1/product_order_income/detail_front/' + id)
}

// 分页条件查询标的订单/理财账户
export function getPageProductOrderReward(param) {
  return fetch('/core/v1/product_order/page_reward_front', {
    pageNum: 1,
    pageSize: 5,
    ...param
  })
}

// 分页条件查询查询团队成员列表
export function getPageTeamMembers(param) {
  return fetch('/core/v1/cuser/team_members', {
    pageNum: 1,
    pageSize: 5,
    ...param
  }).then((res) => {
    res.list = res.list.map((item) => {
      item.extraIncomeRate = Number((Number(item.extraIncomeRate ?? 0) * 100).toFixed(0))
      return item
    })
    return res
  })
}

// 我的钱包
export function getMyAccount(param) {
  return fetch('/core/v1/account/my_account', param)
}

// 奖励钱包-流水
export function getPageAccountIncome(param) {
  return fetch('/core/v1/account_income/page_front', {
    pageNum: 1,
    pageSize: 5,
    ...param
  })
}

// 奖励钱包-流水（按月）
export function getPageAccountIncomeByMonth(param) {
  return fetch('/core/v1/account_income/page_front_by_month', {
    pageNum: 1,
    pageSize: 50,
    ...param
  })
}

// 奖励钱包-交易记录详情
export function getAccountIncomeDetail(id) {
  return fetch('/core/v1/account_income/detail_front/' + id)
}

// 查询团队投币金额总量
export function getTeamMembersSum(userId) {
  return fetch('/core/v1/cuser/team_members_sum', { userId })
}

// 余额划转到冻结金额
export function accountTransfer(amount) {
  return fetch('/core/v1/account/transfer', {
    amount
  })
}

// 股票分红列表
export function getPageVipDividendRecord(param) {
  return fetch('/core/v1/vip_dividend_record/page_front', {
    pageNum: 1,
    pageSize: 50,
    ...param
  })
}

// 前端详情查询股票账户
export function getStockAccount() {
  return fetch('/core/v1/stock_account/detail_front')
}

// 分页条件查询股票账户流水
export function getPageStockJour(param) {
  return fetch('/core/v1/stock_jour/page_front', {
    pageNum: 1,
    pageSize: 50,
    ...param
  })
}

// 详情查询股票账户流水
export function getStockJourDetail(id) {
  return fetch('/core/v1/stock_jour/detail_front/' + id)
}

// 余额划转到锁仓金额
export function productAccountTransfer(param) {
  return fetch('/core/v1/product_account/transfer', param)
}


// 前端详情查询VIP分红记录表
export function getVipDividendAccount(param) {
  return fetch('/core/v1/vip_dividend_record/detail_front', param)
}

