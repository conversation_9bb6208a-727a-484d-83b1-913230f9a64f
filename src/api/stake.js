import fetch from '@/utils/fetch'

export function getStakeList(params) {
  return fetch('/core/v1/product/public/list_show', params).then((result) => {
    const _list = result.filter((item) => item.type == '1')
    return _list.concat(result.filter((item) => item.type != '1'))
  })
}
export function getMarketPrice() {
  return fetch('/core/v1/market/public/finanx_market')
}
export function stakeCreate(params) {
  return fetch('/core/v1/product_order/create', params)
}
export function stakeWithdrawCreate(params) {
  return fetch('/core/v1/product_order_income/create', params)
}
export function getStakeWithdrawSign(params) {
  return fetch('/core/v1/product_order_income/get_redemption', params)
}
export function getAccountWithdrawSign(params) {
  return fetch('/core/v1/account_income/get_redemption', params)
}

export function accountWithdrawCreate(params) {
  return fetch('/core/v1/account_income/create', params)
}

// 账户汇总
export function getProductAccountInfo(params) {
  return fetch('/core/v1/product_account/account_info', params)
}

// 账户汇总
export function getProductOrderSum(params) {
  return fetch('/core/v1/product_order/sum_up', params)
}

// 质押检查
export function productOrderCheck(params) {
  return fetch('/core/v1/product_order/check', params)
}
// 复投
export function productOrderReinvest(params) {
  return fetch('/core/v1/product_order/create_account', params)
}
