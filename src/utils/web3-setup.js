import { projectId, wagmiAdapter, CHAINS } from '../config'
import { createAppKit } from '@reown/appkit/vue'

// 正式
const metadata = {
  name: 'FNXAI',
  description: 'FNXAI',
  url: 'https://app.finanx.ai', // origin must match your domain & subdomain
  icons: ['https://app.finanx.ai/favicon.ico']
}

// Create modal
export const createModal = createAppKit({
  adapters: [wagmiAdapter],
  networks: CHAINS,
  metadata,
  projectId,
  features: {
    email: false,
    socials: false,
    analytics: true // Optional - defaults to your Cloud configuration
  }
})
