import { ElMessage } from 'element-plus'

function error(msg) {
  ElMessage({
    type: 'error',
    message: msg
    // customClass: 'el-message--error' // 这个是由于上面设置了iconClass会覆盖掉type，所以需要再设置
  })
}

function success(msg) {
  ElMessage({
    message: msg,
    type: 'success'
  })
}

function warning(msg) {
  ElMessage({
    message: msg,
    type: 'warning'
    // customClass: 'el-message--error' // 这个是由于上面设置了iconClass会覆盖掉type，所以需要再设置
  })
}

export default { error, success, warning }
