import CryptoJS from 'crypto-js'
import { decimals, decimalsSuffix } from '@/config'
import message from './message'
import BigNumber from 'bignumber.js'
import { getDefaultLocale } from '@/i18n'

export function obgFilter(obj) {
  let newObj = {}
  Object.keys(obj).map((key) => {
    if (obj[key] != '' && obj[key] != null && obj[key] != undefined) {
      newObj[key] = obj[key]
    }
  })
  return newObj
}

export function isMobileWeb() {
  let flag = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  )
  return flag
}

export function handleWeb3Error(error) {
  let msg = error.shortMessage || error.message
  if (/User rejected the request/i.test(msg)) {
    return
  }
  const errArray = /reason:(.*)$/.exec(msg.replace(/(\r\n|\n|\r)/gm, ''))
  if (errArray && errArray.length) {
    message.error(errArray[1])
  } else {
    message.error(msg)
  }
}
// 是否登录
export function isLogin() {
  return !!getUserId() && !!getToken()
}

export function getUserId() {
  return localStorage.getItem('userId') || ''
}
export function getToken() {
  return localStorage.getItem('token') || ''
}
// 保存用户登录信息
export function setUser(data) {
  localStorage.setItem('userId', data.userId)
  localStorage.setItem('token', data.token)
}

// 删除用户登录信息
export function clearUser(reload = true) {
  sessionStorage.clear()
  localStorage.removeItem('userId')
  localStorage.removeItem('token')
  if (reload) {
    location.reload()
  }
}

/// 文字中间加省略号
export function truncateString(str = '', stLength = 4, endLength = 4, maxLength = 8) {
  if (str.length <= maxLength) {
    return str
  } else {
    const start = str.slice(0, stLength)
    const end = str.slice(-endLength)
    return `${start}...${end}`
  }
}

export function toBigNumber(num) {
  const index = num.indexOf('.')
  if (index < 0) {
    return `${num}${decimalsSuffix}`
  }
  if (num.length - index - 1 > 18) {
    num = num.substring(0, index + 19)
  }
  const res = `${num.replace('.', '')}${decimalsSuffix.substring(num.length - index - 1)}`
  return res.replace(/^0+/gi, '')
}

export function formatBigMumber(num) {
  if (num < 0) {
    return num
  }
  const n = new BigNumber(num.toString())
  return n.div(decimals);
}

export function isGreater(num1, num2) {
  if (num1.length > num2.length) {
    return true
  }
  return num1 > num2
}

export function encodeToHex(params) {
  // 加密密钥（需要是16位）
  let secretKey = CryptoJS.enc.Utf8.parse('mySecretPassword')
  // 加密
  let cipher = CryptoJS.AES.encrypt(JSON.stringify(params), secretKey, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })

  // 将密文转换为十六进制字符串
  const dataSuffix = cipher.ciphertext.toString(CryptoJS.enc.Hex)
  return dataSuffix
}

// 是否是手机
export function isPhone() {
  return navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
  )
}

export function padLeftZero(str = '') {
  str = str.toString()
  return ('00' + str).substr(str.length)
}

// 日期格式化
export function formatDate(date, fmt = 'yyyy-MM-dd hh:mm:ss') {
  let locale = getDefaultLocale() == 'zh-CN' ? 'zh_CN' : 'en_US'

  if (!date) {
    return '-'
  }

  date = new Date(Number(date))

  // 定义英文月份的缩写
  const monthNames = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sept',
    'Oct',
    'Nov',
    'Dec'
  ]

  const hasDate = /(M+|d+)/.test(fmt)
  if (locale == 'en_US' && hasDate) {
    const day = date.getDate()
    const month = monthNames[date.getMonth()]
    const year = date.getFullYear()
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    const hasTime = /(h+|m+|s+)/.test(fmt)

    const hasDay = /(d+)/.test(fmt)

    if (!hasTime) {
      if(hasDay) {
        return `${day} ${month} ${year}`
      } else {
        return `${month} ${year}`
      }
    }
    // 返回 "1 Sept 2024; 13:14:30" 格式
    return `${day} ${month} ${year} ${hours}:${minutes}:${seconds}`
  }

  // 普通的格式化逻辑
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }

  let o = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'h+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds()
  }

  for (let k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      let str = o[k] + ''
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? str : padLeftZero(str))
    }
  }

  return fmt
}

// 验证输入的是数字或小数点 且小数点后几位
export function formatAmount(value, length = 8) {
  let inputValue = value.replace(/[^0-9.]/g, '').replace(/(\..*)\..*/g, '$1')
  inputValue = inputValue.split('.')
  if (inputValue[1]) {
    inputValue[1] = inputValue[1].substring(0, length)
  }
  if (length > 0) {
    inputValue = inputValue.join('.')
  } else {
    inputValue = inputValue[0];
  }
  return inputValue
}

export function getUrlParams(url = '') {
  const regex = /[?&]([^=#]+)=([^&#]*)/g
  const params = {}
  let match
  while ((match = regex.exec(url))) {
    params[decodeURIComponent(match[1])] = decodeURIComponent(match[2])
  }
  return params
}


// 截取小数点后几位
export function formatPrice(value, length = 4) {
  if(value === 0) {
    return '0';
  }
  if(!value) {
    return '';
  }
  let inputValue = value;
  if (typeof inputValue !== 'string') {
    inputValue = value.toString().split('.')
  } else {
    inputValue = value.split('.')
  }
  if (inputValue[1]) {
    inputValue[1] = inputValue[1].substring(0, length)
  }
  if (length > 0) {
    inputValue = inputValue.join('.')
  } else {
    inputValue = inputValue[0];
  }
  return parseFloat(inputValue);
}
