import axios from 'axios'
import message from './message'
import { clearUser, getToken, obgFilter } from './index'
import { getDefaultLocale, languagesFetch } from '@/i18n'

const ERR_OK = '200'
const ERR_TIME_OUT = '4'
const ERR_TOKEN = '300000'
const EXPIRE_TOKEN = '300001'
const NULL_TOKEN = '300002'
const INSUFFICIENT_AUTH = '300003'

export default function fetch(url, params) {
  const fetchUrl = '/api' + url
  let _data = obgFilter(params || {})
  let locale = languagesFetch[getDefaultLocale()]
  if (!_data.language) {
    _data.language = locale
  }
  const data = JSON.stringify(_data)

  return axios({
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      Authorization: getToken(),
      'Accept-Language': locale,
      language: locale,
      Client: 'h5'
    },
    url: fetchUrl,
    data: data,
    timeout: 0
  }).then((res) => {
    res = res.data
    const outList = [ERR_TIME_OUT, ERR_TOKEN, EXPIRE_TOKEN, NULL_TOKEN, INSUFFICIENT_AUTH, 'A50004']
    if (outList.includes(res.errorCode)) {
      console.log('res.errorCode', res.errorCode);
      clearUser()
      return Promise.reject('timeout')
    }
    if (res.code !== ERR_OK) {
      const errMsg = res.errorInfo || res.errorMsg || 'error'
      message.error(errMsg)
      return Promise.reject(errMsg)
    }
    return Promise.resolve(res.data)
  })
}
