// import { arbitrum, mainnet, bsc } from '@reown/appkit/networks'
import {
  arbitrumSepolia as arbitrum,
  sepolia as mainnet,
  bscTestnet as bsc
} from '@reown/appkit/networks'

import mainnetAbi from './mainnet_abi'
import arbitrumAbi from './arbitrum_abi'
import bnbAbi from './bnb_abi'

import { http } from '@wagmi/core'

import { WagmiAdapter } from '@reown/appkit-adapter-wagmi'

export const projectId = '9f5bd65b019822eefc3782857d76c1db' // 测试
// export const projectId = '9e706d6d05313418761c5f98b28cb41e' // 线上

// 系统环境 debug/release
export const env = 'debug'
// export const env = 'release'

// 正式地址
// token contract
// export const TokenContractAddressRelease = {
//   [mainnet.id]: '0x939069722D568B5498CcBA4356E800EaefEFD2a5',
//   [arbitrum.id]: '0x3088E120b220E67a2e092F5dA8cdf02ea0170F6A',
//   [bsc.id]: '0xD26889f63094Ba5A9d32666CdF5Ba381acfad6A6'
// }
// // stake contract
// export const StakeContractAddressRelease = {
//   [mainnet.id]: '0x939069722D568B5498CcBA4356E800EaefEFD2a5', // stake & token contract
//   [arbitrum.id]: '0x939069722D568B5498CcBA4356E800EaefEFD2a5',
//   [bsc.id]: '0x939069722D568B5498CcBA4356E800EaefEFD2a5'
// }
// 研发地址
// token contract
export const TokenContractAddressRelease = {
  [mainnet.id]: '0xffF55f1D87D74adB72f31ecbE6bb2b7824498FFF',
  [arbitrum.id]: '0xCDa6b47f28b3C2eF8FD71eAFA05F7ae45C5e7666',
  [bsc.id]: '0xffF55f1D87D74adB72f31ecbE6bb2b7824498FFF'
}
// stake contract
export const StakeContractAddressRelease = {
  [mainnet.id]: '0xffF55f1D87D74adB72f31ecbE6bb2b7824498FFF', // stake & token contract
  [arbitrum.id]: '******************************************',
  [bsc.id]: '******************************************'
}

// token contract
export const TokenContractAddress = TokenContractAddressRelease
// stake contract
export const StakeContractAddress = StakeContractAddressRelease

export const ContractAbi = {
  [mainnet.id]: mainnetAbi,
  [arbitrum.id]: arbitrumAbi,
  [bsc.id]: bnbAbi
}

export const CHAINS = [mainnet, arbitrum, bsc]

export const CHAINS_NAMES = {
  [mainnet.id]: 'Ethereum',
  [arbitrum.id]: 'Arbitrum',
  [bsc.id]: 'BNB'
}
export const CHAINS_NAMES_MAP = {
  [mainnet.id]: 'ETHEREUM',
  [arbitrum.id]: 'ARBITRUM',
  [bsc.id]: 'BNB'
}

// Create Wagmi Adapter / create config
export const wagmiAdapter = new WagmiAdapter({
  projectId,
  networks: CHAINS,
  transports: {
    [mainnet.id]: http(),
    [arbitrum.id]: http(),
    [bsc.id]: http()
  }
})

export const CHAINS_CONFIG = wagmiAdapter.wagmiConfig

export const decimals = 1000000000000000000 // 10^18
export const decimalsSuffix = '000000000000000000'
